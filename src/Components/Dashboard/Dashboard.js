/* eslint-disable eqeqeq */
/* eslint-disable no-unused-vars */
import Nav from 'react-bootstrap/Nav'
import { useSelector, useDispatch } from 'react-redux'
import React, { useState, useEffect, useCallback } from 'react'
import {
  Route,
  Routes,
  Navigate,
  useNavigate,
  useLocation
} from 'react-router-dom'
import axios from 'axios'
import { BASE_URL, instance } from '../../Services/api.service'

import './Dashboard.css'
import Farmers from './Farmers/Farmers'
// import Paravets from "./Paravets/Paravets";
import Cattle from './Cattle/Cattle'
import Tasks from './Tasks/Tasks'
import JobList from './JobList/JobList'
// import FarmerItem from "./Farmers/FarmerItem/FarmerItem";
// import AddFarmer from "./Farmers/AddFarmer/AddFarmer";
import AddTask from './Tasks/AddTask/AddTask'
// import AddCattle from "./Cattle/AddCattle/AddCattle";
// import CattleItem from "./Cattle/CattleItem/CattleItem";
import ToastersService from "../../Services/toasters.service";
import Profile from "./Profile/Profile";
import TaskItem from "./Tasks/TaskItem/TaskItem";
import AddFarmer1 from "./Farmers/AddFarmer/AddFarmer1";
import FarmerItem1 from "./Farmers/FarmerItem/FarmerItem1";
import AddCattle1 from "./Cattle/AddCattle/AddCattle1";
import CattleItem1 from "./Cattle/CattleItem/CattleItem1";
import AddFarmerTask from "./Farmers/FarmerItem/AddFarmerTask/AddFarmerTask";
import Staffs from "./Staffs/Staff";
import StaffItem from "./Staffs/StaffItem/StaffItem";
import ServiceRequests from "./ServiceRequests/ServiceRequests";
import AddStaff from "./Staffs/AddStaff/AddStaff";
import RequisitionList from "./Requisition/RequisitionList";
import AddRequisition from "./Requisition/AddRequisition/AddRequisition";
import PharmacyList from "./Pharmacy/PharmacyList";
import MedicineInventory from "./MedicineInventory/MedicineInventory";
import LedgerList from './Ledger/LedgerList'

import RequisitionDetails from "./Requisition/RequisitionDetails/RequisitionDetails";
import PharmacyRequisitionDetails from "./Pharmacy/PharmacyRequsitionDetails/PharmacyRequisitionDetails";
// import FarmerTaskDetails from "./Farmers/FarmerItem/FarmerTaskDetails/FarmerTaskDetails";
import MapWrapper from './BgLocation/MapWrapper'
import { LocationFilterWrapper } from './BgLocation/LocationFilterWrapper'
import { clearUserToken } from '../user/action'
import { auth } from '../../Config/firebase.config'

// import AuthorizeComponent from '../user/authorizeComponent'

import OpenCommerceRoutes from "../../OpenCommerce/OpenCommerceRoutes";
import AHVetRoutes from "../../AHVet/AHVetRoutes";
import LeadManagementRoutes from "../../LeadManagement/LeadManagementRoutes";
import EntitiesRoutes from "../../Entities/EntitiesRoutes";
import PpuService from "./PPU/PpuService";
import PpuRaiseSr from "./PPU/PpuRaiseSr";
import CustomerServiceRoutes from '../../CustomerService/CustomerServiceRoutes'
import SmartRationRoutes from '../../SmartRation/SmartRationRoutes'
import InventoryManagementRoutes from '../../InventoryManagement/InventoryManagementRoutes'

const { CS_TEAM, Authorize } = require('../user/authorize')
import PharmacyClosedList from "./Pharmacy/PharmacyClosedList";
import Invoice from "./Pharmacy/Invoice/Invoice";

const Dashboard = () => {
  const [menuItems, setMenuItems] = useState([
    {
      id: 1,
      name: 'Farmers',
      icon: 'fa-users',
      isSelected: true,
      path: 'farmers',
      hasSubitems: false
    },
    {
      id: 2,
      name: 'Tasks',
      icon: 'fa-tasks',
      isSelected: false,
      path: 'tasks',
      hasSubitems: false
    },
    {
      id: 3,
      name: 'Staffs',
      icon: 'fa-suitcase',
      isSelected: false,
      path: 'staffs',
      hasSubitems: false
    },
    {
      id: 4,
      name: 'Job List',
      icon: 'fa-list-alt',
      isSelected: false,
      path: 'jobs-list',
      hasSubitems: false
    },
    {
      id: 5,
      name: 'Raise SR',
      icon: 'fa-ticket',
      isSelected: false,
      path: 'service-requests',
      hasSubitems: false
    },
    {
      id: 6,
      name: "Locations",
      icon: "fa-map-marker",
      isSelected: false,
      path: 'bgLocations',
      hasSubitems: false,
      showItem: false,
    },
    {
      id: 7,
      name: "Open Commerce",
      icon: "fa-list-alt",
      isSelected: false,
      path: "oc",
      hasSubitems: false,
    },
    {
      id: 8,
      name: "Freelancers",
      icon: "fa-user-doctor",
      isSelected: false,
      path: "",
      hasSubitems: true,
      subItems: [
        {
          id: 1,
          name: "Onboard",
          icon: "fa-user-plus",
          isSelected: false,
          path: "ppuService",
          hasSubitems: false,
        },
        {
          id: 2,
          name: "Raise SR",
          icon: "fa-ticket",
          isSelected: false,
          path: "ppuServiceRequest",
          hasSubitems: false,
        }
      ]
    },
    {
      id: 9,
      name: "Vet Center",
      icon: "fa-list-alt",
      isSelected: false,
      path: "ahv",
      hasSubitems: false,
    },
    {
      id: 10,
      name: "Customer Service Center",
      icon: "fa-list-alt",
      isSelected: false,
      path: "cs",
      hasSubitems: false,
    },
    {
      id: 11,
      name: "Smart Ration",
      icon: "fa-list-alt",
      isSelected: false,
      path: "sr",
      hasSubitems: false,
    },
    {
      id: 12,
      name: "Lead Management",
      icon: "fa-list-alt",
      isSelected: false,
      path: "lm",
      hasSubitems: false,
    },
    {
      id: 13,
      name: "Inventory Management",
      icon: "fa-list-alt",
      isSelected: false,
      path: "im",
      hasSubitems: false,
    }, {
      id: 14,
      name: "Requisitions",
      icon: "fa-ticket",
      isSelected: false,
      path: "requisitionlist",
      hasSubitems: false,
    },
    {
      id: 13,
      name: "Medicine List",
      icon: "fa-list-alt",
      isSelected: false,
      path: "medicineinventory",
      hasSubitems: false,
    },
    {
      id: 7,
      name: "List of Orders",
      icon: "fa-list-alt",
      isSelected: false,
      path: "pharmacylist",
      hasSubitems: false,
    },
    {
      id: 15,
      name: "Closed Orders",
      icon: "fa-list-alt",
      isSelected: false,
      path: "pharmacyclosedlist",
      hasSubitems: false,
    },
    {
      id: 16,
      name: "Ledger",
      icon: "fa-list-alt",
      isSelected: false,
      path: "ledgerlist",
      hasSubitems: false,
    },
    {
      id: 17,
      name: "Milk Projection",
      icon: "fa-chart-line",
      isSelected: false,
      path: "milk-projection",
      hasSubitems: false,
    },

    // {
    //   id: 6,
    //   name: "Health",
    //   icon: "fa-medkit",
    //   isSelected: false,
    //   path: "test1",
    //   hasSubitems: true,
    //   subItems: [
    //     {
    //       id: 1,
    //       name: "Option 1",
    //       icon: "fa-plus-square",
    //       isSelected: false,
    //       path: "test11",
    //     },
    //     {
    //       id: 2,
    //       name: "Option 2",
    //       icon: "fa-plus-square",
    //       isSelected: false,
    //       path: "test12",
    //     },
    //     {
    //       id: 3,
    //       name: "Option 3",
    //       icon: "fa-plus-square",
    //       isSelected: false,
    //       path: "test13",
    //     },
    //   ],
    // },
    // {
    //   id: 7,
    //   name: "Finance",
    //   icon: "fa-inr",
    //   isSelected: false,
    //   path: "test2",
    //   hasSubitems: true,
    //   subItems: [
    //     {
    //       id: 1,
    //       name: "Option 1",
    //       icon: "fa-medkit",
    //       isSelected: false,
    //       path: "test21",
    //     },
    //     {
    //       id: 2,
    //       name: "Option 2",
    //       icon: "fa-medkit",
    //       isSelected: false,
    //       path: "test22",
    //     },
    //     {
    //       id: 3,
    //       name: "Option 3",
    //       icon: "fa-medkit",
    //       isSelected: false,
    //       path: "test23",
    //     },
    //   ],
    // },
  ])

  let [selectedSubitems, setSelectedSubitems] = useState(null)
  const [showSidebar, setShowSideBar] = useState(true)
  const [profileImage, setProfileImage] = useState()

  const dispatch = useDispatch()
  const navigate = useNavigate()
  const location = useLocation()
  const userData = useSelector(state => state.user.userData)
  const appGroups = useSelector(state => state.user.appGroups)
  const userToken = useSelector((state) => state.user.userToken)
  console.log('appGroups = ', appGroups)
  // console.log(location.pathname);

  const handleResize = () => {
    if (window.innerWidth < 768) {
      setShowSideBar(false)
    } else {
      setShowSideBar(true)
    }
  }

  const getProfileImage = () => {
    if (userData) {
      if (userData.image === null) {
        setProfileImage(
          'https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460__340.png'
        )
      } else {
        setProfileImage(BASE_URL + '/media/' + userData.image)
      }
    }
  }

  useEffect(() => {
    getProfileImage()
    window.addEventListener('resize', handleResize)

    // console.log(location.pathname.split('/'));
    for (const item of menuItems) {
      if (item.path === location.pathname.split('/')[2]) {
        item.isSelected = true
      } else {
        item.isSelected = false
      }
    }
    setMenuItems([...menuItems])
  }, [userData, navigate])

  const toggleButtonHandler = () => {
    setShowSideBar(!showSidebar)
  }

  const menuItemsClickHandler = (item) => {
    selectedSubitems = null
    for (const menu of menuItems) {
      if (item.hasSubitems == true) {
        // handle subitems
        if (item.id === menu.id) {
          menu.isSelected = true
          selectedSubitems = item
          subMenuItemsClickHandler(item.subItems[0]);
        } else {
          menu.isSelected = false
        }
      } else {
        if (item.id === menu.id) {
          menu.isSelected = true
          navigate(item.path)
        } else {
          menu.isSelected = false
        }
      }
    }
    setMenuItems([...menuItems])
    setSelectedSubitems(selectedSubitems)
    handleResize()
  }

  const subMenuItemsClickHandler = (item) => {
    console.log("item => ", item);
    for (const menu of menuItems) {
      if (menu.hasSubitems) {
        for (const subMenu of menu.subItems) {
          if (item.id === subMenu.id) {
            subMenu.isSelected = true
            navigate(item.path)
          } else {
            subMenu.isSelected = false
          }
        }
      }
    }
  }

  const logoutHandler = async () => {
    await auth.signOut()
    dispatch(clearUserToken(navigate, '/login', 'Logged out successfully!'))
  }

  const profileClickHandler = () => {
    navigate('/dashboard/profile')
  }

  if (Object.keys(global.references).length < 1) {
    return null
  } else {
    return (
      <>
        <Nav className="dashboard__navbar">
          {showSidebar === true
            ? (
              <div
                className="dashboard__toggle-btn me-3"
                onClick={toggleButtonHandler}
              >
                <i
                  className="fa fa-times"
                  style={{ fontSize: '20px', cursor: 'pointer' }}
                  aria-hidden="true"
                ></i>
              </div>
            )
            : (
              <div
                className="dashboard__toggle-btn me-3"
                onClick={toggleButtonHandler}
              >
                <i
                  className="fa fa-bars"
                  style={{ fontSize: '20px', cursor: 'pointer' }}
                  aria-hidden="true"
                ></i>
              </div>
            )}

          <div>
            <img
              alt=""
              src={process.env.PUBLIC_URL + 'krushal.png'}
              style={{ width: '110px' }}
            />
          </div>
          <div
            className={
              location.pathname === '/dashboard/profile'
                ? 'dashboard__profile-img-active'
                : 'dashboard__profile-img-inactive'
            }
          >
            <div className="dashboard__img-div" onClick={profileClickHandler}>
              <img
                alt=""
                src={profileImage}
                style={{ width: '100%', height: '100%' }}
              />
            </div>
          </div>
        </Nav>

        {showSidebar === true && (
          <>
            <div
              className={`dashboard__vertical-nav ${selectedSubitems && 'collapsed'
                }`}
            >
              {menuItems.map((item) => (
                <div
                  key={item.id}
                  className={`dashboard__menu-item 
                    ${item.isSelected === true && 'selected'}
                  `}
                  onClick={() => menuItemsClickHandler(item)}
                  data-toggle="tooltip"
                  data-placement="right"
                  title={item.name}
                >
                  <div style={{ width: '30px' }}>
                    <i className={`fa ${item.icon}`} aria-hidden="true"></i>
                  </div>
                  {!selectedSubitems && <div>{item.name}</div>}

                  {item.hasSubitems && !selectedSubitems && (
                    <div style={{ marginLeft: 'auto' }}>
                      <i className="fa fa-angle-right" aria-hidden="true"></i>
                    </div>
                  )}
                </div>
              ))}

              {/* Logout Btn */}
              {!selectedSubitems && (
                <div
                  className="dashboard__menu-item"
                  style={{ position: 'sticky', bottom: 0, width: '100%', zIndex: 999, background: "white" }}
                  onClick={logoutHandler}
                >
                  <i className="fa fa-sign-out" aria-hidden="true"></i> &nbsp;
                  Logout
                </div>
              )}
            </div>

            {/* Secondary sidebar */}
            {selectedSubitems && (
              <div className="dashboard__vertical-nav-subitems">
                <div
                  className={'dashboard__menu-item'}
                  style={{ pointerEvents: 'none' }}
                >
                  <div style={{ width: '30px' }}>
                    <i
                      className={`fa ${selectedSubitems.icon}`}
                      aria-hidden="true"
                    ></i>
                  </div>
                  <div>{selectedSubitems.name}</div>
                </div>

                <hr />

                {selectedSubitems.subItems.map((item) => (
                  <div
                    key={item.id}
                    className={`dashboard__menu-item 
                    ${item.isSelected === true && "selected"}
                  `}
                    onClick={() => subMenuItemsClickHandler(item)}
                  >
                    <div style={{ width: '30px' }}>
                      <i className={`fa ${item.icon}`} aria-hidden="true"></i>
                    </div>
                    <div>{item.name}</div>
                    {item.hasSubitems && (
                      <div style={{ marginLeft: "auto" }}>
                        <i class="fa fa-angle-right" aria-hidden="true"></i>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </>
        )}

        {profileImage && (
          <div className="dashboard__main-content">
            <Routes>
              <Route path="" element={<Navigate to="farmers" />}></Route>

              <Route path="farmers" element={<Farmers />} />
              {/* <Route path="farmers/add-farmer" element={<AddFarmer />} /> */}
              <Route path="farmers/add-farmer1" element={<AddFarmer1 />} />
              {/* <Route path="farmers/:farmerId" element={<FarmerItem />} /> */}
              <Route path="farmers/:farmerId" element={<FarmerItem1 />} />

              {/* <Route path="paravets-list" element={<Paravets />} /> */}

              <Route
                path="farmers/:farmerId/cattle/add-cattle"
                element={<AddCattle1 />}
              />
              {/* <Route path="farmers/cattle/add-cattle" element={<AddCattle />} /> */}
              {/* <Route path="farmers/cattle/:cattleId" element={<CattleItem />} /> */}
              <Route
                path="farmers/:farmerId/cattle/:cattleId"
                element={<CattleItem1 />}
              />

              <Route path="tasks" element={<Tasks />} />
              <Route
                path="farmers/:farmerId/cattle/:cattleId/tasks/add-task"
                element={<AddTask />}
              />
              <Route
                path="farmers/:farmerId/tasks/add-task"
                element={<AddFarmerTask />}
              />
              {/* <Route path="farmers/:farmerId/tasks/:activityId" element={<FarmerTaskDetails />} /> */}
              <Route path="tasks/:taskId" element={<TaskItem />} />

              <Route path="staffs" element={<Staffs />} />
              <Route path="staffs/add-staff" element={<AddStaff />} />
              <Route path="staffs/:staffId" element={<StaffItem />} />

              <Route path="service-requests" element={<ServiceRequests />} />
              <Route path="bgLocations" element={<MapWrapper />} />

              <Route path="requisitionlist" element={<RequisitionList />} />

              <Route path="requisitionlist/requisitiondetails" element={<RequisitionDetails />} />

              <Route path="requisitionlist/addrequisition" element={<AddRequisition />} />


              <Route path="jobs-list" element={<JobList />} />

              <Route path="profile" element={<Profile />} />

              <Route path="bgLocations" element={<MapWrapper />} />

              {/* <Route path="bgLocations" element={<LocationFilterWrapper />} /> */}

              <Route path="ppuService" element={<PpuService />} />
              <Route path="ppuServiceRequest" element={<PpuRaiseSr />} />


              <Route path="bgLocations" element={<MapWrapper />} />

              <Route path="requisitionlist" element={<RequisitionList />} />

              <Route path="requisitionlist/requisitiondetails" element={<RequisitionDetails />} />

              <Route path="requisitionlist/addrequisition" element={<AddRequisition />} />

              <Route path="pharmacylist" element={<PharmacyList />} />

              <Route path="anttabletester" element={<AntdTableTester />} />

              <Route path="pharmacylist/pharmacyrequisitiondetails" element={<PharmacyRequisitionDetails />} />

              <Route path="pharmacyclosedlist" element={<PharmacyClosedList />} />
              <Route path="pharmacyclosedlist/pharmacyrequisitiondetails" element={<PharmacyRequisitionDetails />} />


              <Route path="medicineinventory" element={<MedicineInventory />} />

              <Route path="pharmacyclosedlist/pharmacyrequisitiondetails" element={<PharmacyRequisitionDetails />} />

              <Route path="pharmacylist/pharmacyrequisitiondetails/invoice" element={<Invoice />} />

              <Route path="ledgerlist" element={<LedgerList />} />


              {/* <Route path="ppuStaffs" element={<PpuListTable />}/> */}

            </Routes >
            {/* <Routes>
              <Route path="oc/sellable-animals" element={<SellableAnimals />} />
            </Routes> */}
            < OpenCommerceRoutes />
            <AHVetRoutes />
            <LeadManagementRoutes />
            <EntitiesRoutes />
            <CustomerServiceRoutes />
            <SmartRationRoutes />
            <InventoryManagementRoutes />
          </div >
        )}
      </>
    )
  }
}

// export default Authorize(Dashboard, [CS_TEAM])
export default Dashboard

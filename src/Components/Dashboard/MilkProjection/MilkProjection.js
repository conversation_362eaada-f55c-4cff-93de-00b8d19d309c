import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { Table, Input, Space, Button, Tag, Dropdown, Menu } from 'antd';
import { SearchOutlined, UserOutlined, PhoneOutlined, HomeOutlined, EnvironmentOutlined, IdcardOutlined, TeamOutlined, FilterOutlined, DownOutlined } from '@ant-design/icons';
import { fetchFarmers } from './api';

const MilkProjection = () => {
  const userToken = useSelector((state) => state.user.userToken);
  const navigate = useNavigate();
  const [farmers, setFarmers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState([]);
  const [availableFilters, setAvailableFilters] = useState([]);
  const [showFilterPopup, setShowFilterPopup] = useState(false);

  // Table columns configuration
  const getColumns = () => [
    {
      title: (
        <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
          <IdcardOutlined style={{ color: '#667eea' }} />
          <span>Visual ID</span>
        </div>
      ),
      dataIndex: 'customer_visual_id',
      key: 'customer_visual_id',
      width: 130,
      render: (text) => (
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: 8,
          padding: '4px 0'
        }}>
          <div style={{
            background: 'linear-gradient(135deg, #667eea, #764ba2)',
            color: '#fff',
            borderRadius: '6px',
            padding: '4px 8px',
            fontSize: '12px',
            fontWeight: 600,
            letterSpacing: '0.5px'
          }}>
            {text || 'N/A'}
          </div>
        </div>
      ),
    },
    {
      title: (
        <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
          <UserOutlined style={{ color: '#52c41a' }} />
          <span>Farmer Name</span>
        </div>
      ),
      dataIndex: 'customer_name',
      key: 'customer_name',
      width: 180,
      render: (text) => (
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: 10,
          padding: '4px 0'
        }}>
          <div style={{
            width: '36px',
            height: '36px',
            borderRadius: '50%',
            background: 'linear-gradient(135deg, #52c41a, #73d13d)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#fff',
            fontWeight: 600,
            fontSize: '14px'
          }}>
            {text ? text.charAt(0).toUpperCase() : 'F'}
          </div>
          <div>
            <div style={{
              fontWeight: 600,
              color: '#262626',
              fontSize: '14px'
            }}>
              {text || 'Unknown'}
            </div>
          </div>
        </div>
      ),
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ padding: 8 }}>
          <Input
            placeholder="Search name"
            value={selectedKeys[0]}
            onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => confirm()}
            style={{ width: 188, marginBottom: 8, display: 'block' }}
          />
          <Space>
            <Button
              type="primary"
              onClick={() => confirm()}
              icon={<SearchOutlined />}
              size="small"
              style={{ width: 90 }}
            >
              Search
            </Button>
            <Button onClick={() => clearFilters()} size="small" style={{ width: 90 }}>
              Reset
            </Button>
          </Space>
        </div>
      ),
      filterIcon: (filtered) => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
      onFilter: (value, record) =>
        record.customer_name?.toLowerCase().includes(value.toLowerCase()),
    },
    {
      title: 'Mobile',
      dataIndex: 'mobile_number',
      key: 'mobile_number',
      width: 130,
      render: (text) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
          <PhoneOutlined style={{ color: '#fa8c16' }} />
          <span>{text || '-'}</span>
        </div>
      ),
    },
    {
      title: 'Alt Contact',
      dataIndex: 'alt_contact_number',
      key: 'alt_contact_number',
      width: 130,
      render: (text) => (
        <span>{text || '-'}</span>
      ),
    },
    {
      title: 'Village',
      dataIndex: 'village',
      key: 'village',
      width: 120,
      render: (text) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
          <HomeOutlined style={{ color: '#722ed1' }} />
          <span>{text || '-'}</span>
        </div>
      ),
      filters: Array.from(new Set(farmers.map(f => f.village).filter(Boolean))).map(village => ({
        text: village,
        value: village,
      })),
      onFilter: (value, record) => record.village === value,
    },
    {
      title: 'Taluk',
      dataIndex: 'taluk',
      key: 'taluk',
      width: 120,
      render: (text) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
          <EnvironmentOutlined style={{ color: '#eb2f96' }} />
          <span>{text || '-'}</span>
        </div>
      ),
    },
    {
      title: 'LSS/Paravet',
      dataIndex: 'staff_name',
      key: 'staff_name',
      width: 150,
      render: (text) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
          <TeamOutlined style={{ color: '#13c2c2' }} />
          <span>{text || '-'}</span>
        </div>
      ),
    },
    {
      title: 'T&C',
      dataIndex: 'terms',
      key: 'terms',
      width: 100,
      render: (text) => (
        <Tag color={text ? 'green' : 'default'}>{text || '-'}</Tag>
      ),
    },
    {
      title: 'Pending Subscription',
      dataIndex: 'active_unsubscribed_animals',
      key: 'active_unsubscribed_animals',
      width: 150,
      render: (text) => (
        <Tag color={text > 0 ? 'orange' : 'green'}>{text || 0}</Tag>
      ),
    },
  ];

  useEffect(() => {
    const fetchData = async () => {
      if (!userToken) return;
      setLoading(true);
      try {
        const farmersData = await fetchFarmers(userToken);
        setFarmers(farmersData);

        // Generate filter labels from data
        const villages = [...new Set(farmersData.map(f => f.village).filter(Boolean))];
        const taluks = [...new Set(farmersData.map(f => f.taluk).filter(Boolean))];
        const staffNames = [...new Set(farmersData.map(f => f.staff_name).filter(Boolean))];

        const filters = [
          ...villages.map(village => ({ type: 'village', value: village, label: `📍 ${village}`, color: '#722ed1' })),
          ...taluks.map(taluk => ({ type: 'taluk', value: taluk, label: `🏛️ ${taluk}`, color: '#eb2f96' })),
          ...staffNames.map(staff => ({ type: 'staff', value: staff, label: `👨‍⚕️ ${staff}`, color: '#13c2c2' })),
          { type: 'status', value: 'has_pending', label: '⚠️ Has Pending Subscriptions', color: '#fa8c16' },
          { type: 'status', value: 'no_pending', label: '✅ No Pending Subscriptions', color: '#52c41a' },
        ];

        setAvailableFilters(filters);
      } catch (error) {
        setFarmers([]);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [userToken]);

  const handleFilterToggle = (filter) => {
    setSelectedFilters(prev => {
      const isSelected = prev.some(f => f.type === filter.type && f.value === filter.value);
      if (isSelected) {
        return prev.filter(f => !(f.type === filter.type && f.value === filter.value));
      } else {
        return [...prev, filter];
      }
    });
    setShowFilterPopup(false); // Close popup after selection
  };

  const clearAllFilters = () => {
    setSelectedFilters([]);
    setShowFilterPopup(false);
  };

  // Filter farmers based on selected filters
  const filteredFarmers = farmers.filter(farmer => {
    if (selectedFilters.length === 0) return true;

    return selectedFilters.every(filter => {
      switch (filter.type) {
        case 'village':
          return farmer.village === filter.value;
        case 'taluk':
          return farmer.taluk === filter.value;
        case 'staff':
          return farmer.staff_name === filter.value;
        case 'status':
          if (filter.value === 'has_pending') {
            return (farmer.active_unsubscribed_animals || 0) > 0;
          } else if (filter.value === 'no_pending') {
            return (farmer.active_unsubscribed_animals || 0) === 0;
          }
          return true;
        default:
          return true;
      }
    });
  });

  // Create filter popup content
  const filterPopupContent = (
    <div style={{
      padding: '16px',
      maxWidth: '400px',
      maxHeight: '500px',
      overflowY: 'auto'
    }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '16px',
        borderBottom: '1px solid #f0f0f0',
        paddingBottom: '12px'
      }}>
        <span style={{ fontWeight: 600, fontSize: '16px' }}>Filter Options</span>
        {selectedFilters.length > 0 && (
          <Button
            size="small"
            type="link"
            onClick={clearAllFilters}
            style={{ color: '#ff4d4f' }}
          >
            Clear All ({selectedFilters.length})
          </Button>
        )}
      </div>

      <div style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '8px'
      }}>
        {availableFilters.map((filter, index) => {
          const isSelected = selectedFilters.some(f => f.type === filter.type && f.value === filter.value);
          return (
            <div
              key={`${filter.type}-${filter.value}-${index}`}
              onClick={() => handleFilterToggle(filter)}
              style={{
                background: isSelected ? filter.color : '#fff',
                color: isSelected ? '#fff' : filter.color,
                border: `2px solid ${filter.color}`,
                borderRadius: '8px',
                padding: '8px 12px',
                fontSize: '14px',
                fontWeight: 500,
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                userSelect: 'none',
                boxShadow: isSelected ? `0 2px 8px ${filter.color}30` : '0 1px 3px rgba(0,0,0,0.1)'
              }}
            >
              {filter.label}
            </div>
          );
        })}
      </div>
    </div>
  );

  return (
    <div style={{
      width: '100vw',
      height: '100vh',
      padding: 0,
      margin: 0,
      background: '#fff',
      boxSizing: 'border-box',
      overflow: 'hidden',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Header */}
      <div style={{
        padding: '20px 32px',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        borderBottom: 'none',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        gap: 12,
        flexShrink: 0,
        height: '72px',
        boxSizing: 'border-box',
        boxShadow: '0 4px 20px rgba(102, 126, 234, 0.15)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
          <div style={{
            background: 'rgba(255, 255, 255, 0.2)',
            borderRadius: '12px',
            padding: '8px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <TeamOutlined style={{ color: '#fff', fontSize: '24px' }} />
          </div>
          <div>
            <span style={{
              fontSize: '24px',
              fontWeight: 700,
              color: '#fff',
              letterSpacing: '-0.5px'
            }}>
              Farmers Directory
            </span>
            <div style={{
              fontSize: '14px',
              color: 'rgba(255, 255, 255, 0.8)',
              fontWeight: 400,
              marginTop: '2px'
            }}>
              Milk Projection Management
            </div>
          </div>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <div style={{
            background: 'rgba(255, 255, 255, 0.15)',
            borderRadius: '8px',
            padding: '8px 16px',
            color: '#fff',
            fontSize: '14px',
            fontWeight: 500
          }}>
            {filteredFarmers.length} of {farmers.length} Farmers
          </div>

          <Dropdown
            dropdownRender={() => filterPopupContent}
            trigger={['click']}
            open={showFilterPopup}
            onOpenChange={setShowFilterPopup}
            placement="bottomRight"
          >
            <Button
              style={{
                background: selectedFilters.length > 0 ? 'rgba(255, 255, 255, 0.9)' : 'rgba(255, 255, 255, 0.2)',
                border: 'none',
                color: selectedFilters.length > 0 ? '#667eea' : '#fff',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                fontWeight: 500
              }}
            >
              <FilterOutlined />
              Filters
              {selectedFilters.length > 0 && (
                <span style={{
                  background: '#667eea',
                  color: '#fff',
                  borderRadius: '10px',
                  padding: '2px 6px',
                  fontSize: '11px',
                  minWidth: '18px',
                  textAlign: 'center'
                }}>
                  {selectedFilters.length}
                </span>
              )}
              <DownOutlined style={{ fontSize: '10px' }} />
            </Button>
          </Dropdown>
        </div>
      </div>

      {/* Table Container */}
      <div style={{
        flex: 1,
        overflow: 'hidden',
        background: '#fff'
      }}>
        <Table
          columns={getColumns()}
          dataSource={filteredFarmers}
          rowKey="customer_id"
          loading={loading}
          pagination={false}
          onRow={(record) => ({
            onClick: () => navigate(`/farmer/${record.customer_id}/animals`, { state: { farmer: record } }),
            style: { cursor: 'pointer' },
          })}
          scroll={{
            x: 1400,
            y: 'calc(100vh - 144px)'
          }}
          size="middle"
          style={{
            background: '#fff',
            height: '100%',
          }}
          rowClassName={(_, index) =>
            index % 2 === 0 ? 'table-row-even' : 'table-row-odd'
          }
        />
      </div>

      <style>{`
        /* Reset any default margins/padding */
        * {
          box-sizing: border-box;
        }

        body, html {
          margin: 0 !important;
          padding: 0 !important;
          overflow: hidden !important;
          height: 100vh !important;
        }

        /* Table styling */
        .table-row-even:hover,
        .table-row-odd:hover {
          background-color: #e6f7ff !important;
        }

        .table-row-even {
          background-color: #fafafa !important;
        }

        .table-row-odd {
          background-color: #ffffff !important;
        }

        .ant-table-tbody > tr > td {
          padding: 12px 16px !important;
          border-bottom: 1px solid #f0f0f0 !important;
        }

        .ant-table-thead > tr > th {
          background: #f5f5f5 !important;
          font-weight: 600 !important;
          padding: 16px !important;
          border-bottom: 2px solid #e8e8e8 !important;
          position: sticky !important;
          top: 0 !important;
          z-index: 10 !important;
        }

        /* Full height table */
        .ant-table-wrapper {
          height: 100% !important;
          overflow: hidden !important;
        }

        .ant-table {
          height: 100% !important;
        }

        .ant-table-container {
          height: calc(100% - 56px) !important;
          overflow: auto !important;
        }

        .ant-table-body {
          height: 100% !important;
          overflow-x: auto !important;
          overflow-y: auto !important;
        }

        /* Remove pagination styling since we're using scroll */

        /* Horizontal scroll styling */
        .ant-table-body::-webkit-scrollbar {
          height: 8px;
          width: 8px;
        }

        .ant-table-body::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 4px;
        }

        .ant-table-body::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 4px;
        }

        .ant-table-body::-webkit-scrollbar-thumb:hover {
          background: #a8a8a8;
        }

        /* Loading overlay */
        .ant-spin-container {
          height: 100% !important;
        }

        /* Filter scrollbar styling */
        div[style*="overflowX: auto"]::-webkit-scrollbar {
          height: 6px;
        }

        div[style*="overflowX: auto"]::-webkit-scrollbar-track {
          background: #f0f0f0;
          border-radius: 3px;
        }

        div[style*="overflowX: auto"]::-webkit-scrollbar-thumb {
          background: #d9d9d9;
          border-radius: 3px;
        }

        div[style*="overflowX: auto"]::-webkit-scrollbar-thumb:hover {
          background: #bfbfbf;
        }

        /* Filter animation */
        @keyframes filterPulse {
          0% { transform: scale(1); }
          50% { transform: scale(1.05); }
          100% { transform: scale(1); }
        }

        /* Smooth transitions */
        * {
          transition: all 0.2s ease;
        }
      `}</style>
    </div>
  );
};

export default MilkProjection; 
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { Table, Input, Space, Button, Tag } from 'antd';
import { SearchOutlined, UserOutlined, PhoneOutlined, HomeOutlined, EnvironmentOutlined, IdcardOutlined, TeamOutlined } from '@ant-design/icons';
import { fetchFarmers } from './api';

const MilkProjection = () => {
  const userToken = useSelector((state) => state.user.userToken);
  const navigate = useNavigate();
  const [farmers, setFarmers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // Table columns configuration
  const getColumns = () => [
    {
      title: (
        <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
          <IdcardOutlined style={{ color: '#667eea' }} />
          <span>Visual ID</span>
        </div>
      ),
      dataIndex: 'customer_visual_id',
      key: 'customer_visual_id',
      width: 130,
      render: (text) => (
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: 8,
          padding: '4px 0'
        }}>
          <div style={{
            background: 'linear-gradient(135deg, #667eea, #764ba2)',
            color: '#fff',
            borderRadius: '6px',
            padding: '4px 8px',
            fontSize: '12px',
            fontWeight: 600,
            letterSpacing: '0.5px'
          }}>
            {text || 'N/A'}
          </div>
        </div>
      ),
    },
    {
      title: (
        <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
          <UserOutlined style={{ color: '#52c41a' }} />
          <span>Farmer Name</span>
        </div>
      ),
      dataIndex: 'customer_name',
      key: 'customer_name',
      width: 180,
      render: (text) => (
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: 10,
          padding: '4px 0'
        }}>
          <div style={{
            width: '36px',
            height: '36px',
            borderRadius: '50%',
            background: 'linear-gradient(135deg, #52c41a, #73d13d)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#fff',
            fontWeight: 600,
            fontSize: '14px'
          }}>
            {text ? text.charAt(0).toUpperCase() : 'F'}
          </div>
          <div>
            <div style={{
              fontWeight: 600,
              color: '#262626',
              fontSize: '14px'
            }}>
              {text || 'Unknown'}
            </div>
          </div>
        </div>
      ),
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ padding: 8 }}>
          <Input
            placeholder="Search name"
            value={selectedKeys[0]}
            onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => confirm()}
            style={{ width: 188, marginBottom: 8, display: 'block' }}
          />
          <Space>
            <Button
              type="primary"
              onClick={() => confirm()}
              icon={<SearchOutlined />}
              size="small"
              style={{ width: 90 }}
            >
              Search
            </Button>
            <Button onClick={() => clearFilters()} size="small" style={{ width: 90 }}>
              Reset
            </Button>
          </Space>
        </div>
      ),
      filterIcon: (filtered) => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
      onFilter: (value, record) =>
        record.customer_name?.toLowerCase().includes(value.toLowerCase()),
    },
    {
      title: 'Mobile',
      dataIndex: 'mobile_number',
      key: 'mobile_number',
      width: 130,
      render: (text) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
          <PhoneOutlined style={{ color: '#fa8c16' }} />
          <span>{text || '-'}</span>
        </div>
      ),
    },
    {
      title: 'Alt Contact',
      dataIndex: 'alt_contact_number',
      key: 'alt_contact_number',
      width: 130,
      render: (text) => (
        <span>{text || '-'}</span>
      ),
    },
    {
      title: 'Village',
      dataIndex: 'village',
      key: 'village',
      width: 120,
      render: (text) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
          <HomeOutlined style={{ color: '#722ed1' }} />
          <span>{text || '-'}</span>
        </div>
      ),
      filters: Array.from(new Set(farmers.map(f => f.village).filter(Boolean))).map(village => ({
        text: village,
        value: village,
      })),
      onFilter: (value, record) => record.village === value,
    },
    {
      title: 'Taluk',
      dataIndex: 'taluk',
      key: 'taluk',
      width: 120,
      render: (text) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
          <EnvironmentOutlined style={{ color: '#eb2f96' }} />
          <span>{text || '-'}</span>
        </div>
      ),
    },
    {
      title: 'LSS/Paravet',
      dataIndex: 'staff_name',
      key: 'staff_name',
      width: 150,
      render: (text) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
          <TeamOutlined style={{ color: '#13c2c2' }} />
          <span>{text || '-'}</span>
        </div>
      ),
    },
    {
      title: 'T&C',
      dataIndex: 'terms',
      key: 'terms',
      width: 100,
      render: (text) => (
        <Tag color={text ? 'green' : 'default'}>{text || '-'}</Tag>
      ),
    },
    {
      title: 'Pending Subscription',
      dataIndex: 'active_unsubscribed_animals',
      key: 'active_unsubscribed_animals',
      width: 150,
      render: (text) => (
        <Tag color={text > 0 ? 'orange' : 'green'}>{text || 0}</Tag>
      ),
    },
  ];

  useEffect(() => {
    const fetchData = async () => {
      if (!userToken) return;
      setLoading(true);
      try {
        const farmersData = await fetchFarmers(userToken);
        setFarmers(farmersData);
        setPagination(prev => ({ ...prev, total: farmersData.length }));
      } catch (error) {
        setFarmers([]);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [userToken]);

  const handleTableChange = (paginationConfig) => {
    setPagination(paginationConfig);
  };

  return (
    <div style={{
      width: '100vw',
      height: '100vh',
      padding: 0,
      margin: 0,
      background: '#fff',
      boxSizing: 'border-box',
      overflow: 'hidden',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Header */}
      <div style={{
        padding: '20px 32px',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        borderBottom: 'none',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        gap: 12,
        flexShrink: 0,
        height: '72px',
        boxSizing: 'border-box',
        boxShadow: '0 4px 20px rgba(102, 126, 234, 0.15)'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
          <div style={{
            background: 'rgba(255, 255, 255, 0.2)',
            borderRadius: '12px',
            padding: '8px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <TeamOutlined style={{ color: '#fff', fontSize: '24px' }} />
          </div>
          <div>
            <span style={{
              fontSize: '24px',
              fontWeight: 700,
              color: '#fff',
              letterSpacing: '-0.5px'
            }}>
              Farmers Directory
            </span>
            <div style={{
              fontSize: '14px',
              color: 'rgba(255, 255, 255, 0.8)',
              fontWeight: 400,
              marginTop: '2px'
            }}>
              Milk Projection Management
            </div>
          </div>
        </div>
        <div style={{
          background: 'rgba(255, 255, 255, 0.15)',
          borderRadius: '8px',
          padding: '8px 16px',
          color: '#fff',
          fontSize: '14px',
          fontWeight: 500
        }}>
          {farmers.length} Total Farmers
        </div>
      </div>

      {/* Table Container */}
      <div style={{
        flex: 1,
        overflow: 'hidden',
        background: '#fff'
      }}>
        <Table
          columns={getColumns()}
          dataSource={farmers}
          rowKey="customer_id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} farmers`,
            pageSizeOptions: ['10', '20', '50', '100'],
            position: ['bottomCenter'],
            style: { padding: '16px 24px' }
          }}
          onChange={handleTableChange}
          onRow={(record) => ({
            onClick: () => navigate(`/farmer/${record.customer_id}/animals`, { state: { farmer: record } }),
            style: { cursor: 'pointer' },
          })}
          scroll={{
            x: 1400,
            y: 'calc(100vh - 128px)'
          }}
          size="middle"
          style={{
            background: '#fff',
            height: '100%',
          }}
          rowClassName={(_, index) =>
            index % 2 === 0 ? 'table-row-even' : 'table-row-odd'
          }
        />
      </div>

      <style>{`
        /* Reset any default margins/padding */
        * {
          box-sizing: border-box;
        }

        body, html {
          margin: 0 !important;
          padding: 0 !important;
          overflow: hidden !important;
          height: 100vh !important;
        }

        /* Table styling */
        .table-row-even:hover,
        .table-row-odd:hover {
          background-color: #e6f7ff !important;
        }

        .table-row-even {
          background-color: #fafafa !important;
        }

        .table-row-odd {
          background-color: #ffffff !important;
        }

        .ant-table-tbody > tr > td {
          padding: 12px 16px !important;
          border-bottom: 1px solid #f0f0f0 !important;
        }

        .ant-table-thead > tr > th {
          background: #f5f5f5 !important;
          font-weight: 600 !important;
          padding: 16px !important;
          border-bottom: 2px solid #e8e8e8 !important;
          position: sticky !important;
          top: 0 !important;
          z-index: 10 !important;
        }

        /* Full height table */
        .ant-table-wrapper {
          height: 100% !important;
          overflow: hidden !important;
        }

        .ant-table {
          height: 100% !important;
        }

        .ant-table-container {
          height: calc(100% - 56px) !important;
          overflow: auto !important;
        }

        .ant-table-body {
          height: 100% !important;
          overflow-x: auto !important;
          overflow-y: auto !important;
        }

        /* Pagination styling */
        .ant-pagination {
          margin: 0 !important;
          padding: 16px 24px !important;
          background: #fafafa !important;
          border-top: 1px solid #f0f0f0 !important;
        }

        /* Horizontal scroll styling */
        .ant-table-body::-webkit-scrollbar {
          height: 8px;
          width: 8px;
        }

        .ant-table-body::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 4px;
        }

        .ant-table-body::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 4px;
        }

        .ant-table-body::-webkit-scrollbar-thumb:hover {
          background: #a8a8a8;
        }

        /* Loading overlay */
        .ant-spin-container {
          height: 100% !important;
        }
      `}</style>
    </div>
  );
};

export default MilkProjection; 
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { Table, Input, Tag } from 'antd';
import { SearchOutlined, UserOutlined, PhoneOutlined, HomeOutlined, EnvironmentOutlined, IdcardOutlined, TeamOutlined } from '@ant-design/icons';
import { fetchFarmers } from './api';

const MilkProjection = () => {
  const userToken = useSelector((state) => state.user.userToken);
  const navigate = useNavigate();
  const [farmers, setFarmers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(50);
  const [searchFilters, setSearchFilters] = useState({
    name: '',
    mobile: '',
    village: '',
    taluk: '',
    staff: '',
    visualId: ''
  });

  // Debouncing hook
  const useDebounce = (value, delay) => {
    const [debouncedValue, setDebouncedValue] = useState(value);

    useEffect(() => {
      const handler = setTimeout(() => {
        setDebouncedValue(value);
      }, delay);

      return () => {
        clearTimeout(handler);
      };
    }, [value, delay]);

    return debouncedValue;
  };

  const debouncedSearchFilters = useDebounce(searchFilters, 300);

  // Handle filter changes
  const handleFilterChange = (field, value) => {
    setSearchFilters(prev => ({
      ...prev,
      [field]: value
    }));
    setCurrentPage(1); // Reset to first page when filtering
  };

  // Table columns configuration with integrated filters
  const getColumns = () => [
    {
      title: (
        <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
            <IdcardOutlined style={{ color: '#667eea' }} />
            <span>Visual ID</span>
          </div>
          <Input
            placeholder="Filter ID"
            value={searchFilters.visualId}
            onChange={(e) => handleFilterChange('visualId', e.target.value)}
            size="small"
            style={{
              fontSize: '11px',
              width: '100%',
              maxWidth: '140px'
            }}
            prefix={<SearchOutlined style={{ color: '#bfbfbf', fontSize: '10px' }} />}
          />
        </div>
      ),
      dataIndex: 'customer_visual_id',
      key: 'customer_visual_id',
      width: 160,
      minWidth: 160,
      render: (text) => (
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: 8,
          padding: '4px 0'
        }}>
          <div style={{
            background: 'linear-gradient(135deg, #667eea, #764ba2)',
            color: '#fff',
            borderRadius: '6px',
            padding: '4px 8px',
            fontSize: '12px',
            fontWeight: 600,
            letterSpacing: '0.5px'
          }}>
            {text || 'N/A'}
          </div>
        </div>
      ),
    },
    {
      title: (
        <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
            <UserOutlined style={{ color: '#52c41a' }} />
            <span>Farmer Name</span>
          </div>
          <Input
            placeholder="Filter name"
            value={searchFilters.name}
            onChange={(e) => handleFilterChange('name', e.target.value)}
            size="small"
            style={{
              fontSize: '11px',
              width: '100%',
              maxWidth: '180px'
            }}
            prefix={<SearchOutlined style={{ color: '#bfbfbf', fontSize: '10px' }} />}
          />
        </div>
      ),
      dataIndex: 'customer_name',
      key: 'customer_name',
      width: 220,
      minWidth: 220,
      render: (text) => (
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: 'clamp(6px, 1vw, 10px)',
          padding: '4px 0'
        }}>
          <div
            className="farmer-name-circle"
            style={{
              width: 'clamp(28px, 3vw, 36px)',
              height: 'clamp(28px, 3vw, 36px)',
              borderRadius: '50%',
              background: 'linear-gradient(135deg, #52c41a, #73d13d)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#fff',
              fontWeight: 600,
              fontSize: 'clamp(10px, 1.2vw, 14px)',
              flexShrink: 0,
              transition: 'all 0.2s ease'
            }}
          >
            {text ? text.charAt(0).toUpperCase() : 'F'}
          </div>
          <div style={{ minWidth: 0, flex: 1 }}>
            <div
              className="farmer-name-text"
              style={{
                fontWeight: 600,
                color: '#262626',
                fontSize: 'clamp(12px, 1.4vw, 14px)',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                transition: 'all 0.2s ease'
              }}
            >
              {text || 'Unknown'}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: (
        <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
            <PhoneOutlined style={{ color: '#fa8c16' }} />
            <span>Mobile</span>
          </div>
          <Input
            placeholder="Filter mobile"
            value={searchFilters.mobile}
            onChange={(e) => handleFilterChange('mobile', e.target.value)}
            size="small"
            style={{
              fontSize: '11px',
              width: '100%',
              maxWidth: '150px'
            }}
            prefix={<SearchOutlined style={{ color: '#bfbfbf', fontSize: '10px' }} />}
          />
        </div>
      ),
      dataIndex: 'mobile_number',
      key: 'mobile_number',
      width: 170,
      minWidth: 170,
      render: (text) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
          <PhoneOutlined style={{ color: '#fa8c16' }} />
          <span style={{ fontWeight: 500 }}>{text || '-'}</span>
        </div>
      ),
    },
    {
      title: 'Alt Contact',
      dataIndex: 'alt_contact_number',
      key: 'alt_contact_number',
      width: 140,
      minWidth: 140,
      render: (text) => (
        <span style={{ color: '#595959' }}>{text || '-'}</span>
      ),
    },
    {
      title: (
        <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
            <HomeOutlined style={{ color: '#722ed1' }} />
            <span>Village</span>
          </div>
          <Input
            placeholder="Filter village"
            value={searchFilters.village}
            onChange={(e) => handleFilterChange('village', e.target.value)}
            size="small"
            style={{
              fontSize: '11px',
              width: '100%',
              maxWidth: '140px'
            }}
            prefix={<SearchOutlined style={{ color: '#bfbfbf', fontSize: '10px' }} />}
          />
        </div>
      ),
      dataIndex: 'village',
      key: 'village',
      width: 160,
      minWidth: 160,
      render: (text) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
          <HomeOutlined style={{ color: '#722ed1' }} />
          <span style={{ fontWeight: 500 }}>{text || '-'}</span>
        </div>
      ),
    },
    {
      title: (
        <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
            <EnvironmentOutlined style={{ color: '#eb2f96' }} />
            <span>Taluk</span>
          </div>
          <Input
            placeholder="Filter taluk"
            value={searchFilters.taluk}
            onChange={(e) => handleFilterChange('taluk', e.target.value)}
            size="small"
            style={{
              fontSize: '11px',
              width: '100%',
              maxWidth: '140px'
            }}
            prefix={<SearchOutlined style={{ color: '#bfbfbf', fontSize: '10px' }} />}
          />
        </div>
      ),
      dataIndex: 'taluk',
      key: 'taluk',
      width: 160,
      minWidth: 160,
      render: (text) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
          <EnvironmentOutlined style={{ color: '#eb2f96' }} />
          <span style={{ fontWeight: 500 }}>{text || '-'}</span>
        </div>
      ),
    },
    {
      title: (
        <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
            <TeamOutlined style={{ color: '#13c2c2' }} />
            <span>LSS/Paravet</span>
          </div>
          <Input
            placeholder="Filter staff"
            value={searchFilters.staff}
            onChange={(e) => handleFilterChange('staff', e.target.value)}
            size="small"
            style={{
              fontSize: '11px',
              width: '100%',
              maxWidth: '160px'
            }}
            prefix={<SearchOutlined style={{ color: '#bfbfbf', fontSize: '10px' }} />}
          />
        </div>
      ),
      dataIndex: 'staff_name',
      key: 'staff_name',
      width: 180,
      minWidth: 180,
      render: (text) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
          <TeamOutlined style={{ color: '#13c2c2' }} />
          <span style={{ fontWeight: 500 }}>{text || '-'}</span>
        </div>
      ),
    },
    {
      title: 'T&C',
      dataIndex: 'terms',
      key: 'terms',
      width: 120,
      minWidth: 120,
      render: (text) => (
        <Tag color={text ? 'green' : 'default'}>{text || '-'}</Tag>
      ),
    },
    {
      title: 'Pending Subscription',
      dataIndex: 'active_unsubscribed_animals',
      key: 'active_unsubscribed_animals',
      width: 180,
      minWidth: 180,
      render: (text) => (
        <Tag color={text > 0 ? 'orange' : 'green'}>{text || 0}</Tag>
      ),
    },
  ];

  useEffect(() => {
    const fetchData = async () => {
      if (!userToken) return;
      setLoading(true);
      try {
        const farmersData = await fetchFarmers(userToken);
        setFarmers(farmersData);
      } catch (error) {
        setFarmers([]);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [userToken]);

  // Filter farmers based on search filters
  const filteredFarmers = farmers.filter(farmer => {
    const matchesVisualId = !debouncedSearchFilters.visualId ||
      (farmer.customer_visual_id || '').toLowerCase().includes(debouncedSearchFilters.visualId.toLowerCase());

    const matchesName = !debouncedSearchFilters.name ||
      (farmer.customer_name || '').toLowerCase().includes(debouncedSearchFilters.name.toLowerCase());

    const matchesMobile = !debouncedSearchFilters.mobile ||
      (farmer.mobile_number || '').replace(/\s|\-/g, '').includes(debouncedSearchFilters.mobile.replace(/\s|\-/g, ''));

    const matchesVillage = !debouncedSearchFilters.village ||
      (farmer.village || '').toLowerCase().includes(debouncedSearchFilters.village.toLowerCase());

    const matchesTaluk = !debouncedSearchFilters.taluk ||
      (farmer.taluk || '').toLowerCase().includes(debouncedSearchFilters.taluk.toLowerCase());

    const matchesStaff = !debouncedSearchFilters.staff ||
      (farmer.staff_name || '').toLowerCase().includes(debouncedSearchFilters.staff.toLowerCase());

    return matchesVisualId && matchesName && matchesMobile && matchesVillage && matchesTaluk && matchesStaff;
  });

  // Pagination logic
  const totalPages = Math.ceil(filteredFarmers.length / pageSize);

  // Handle scroll to load more
  const handleScroll = (e) => {
    const { target } = e;
    const { scrollTop, scrollHeight, clientHeight } = target;

    // Load more when scrolled to bottom
    if (scrollHeight - scrollTop === clientHeight && currentPage < totalPages) {
      setCurrentPage(prev => prev + 1);
    }
  };

  // Get farmers to display (with infinite scroll pagination)
  const displayedFarmers = filteredFarmers.slice(0, currentPage * pageSize);

  return (
    <div style={{
      width: '100%',
      height: '100vh',
      padding: 0,
      margin: 0,
      background: '#fff',
      boxSizing: 'border-box',
      overflow: 'hidden',
      display: 'flex',
      flexDirection: 'column',
      position: 'relative'
    }}>
      {/* Header */}
      <div style={{
        padding: '12px 16px',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        borderBottom: 'none',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        gap: 8,
        flexShrink: 0,
        height: '60px',
        boxSizing: 'border-box',
        boxShadow: '0 2px 8px rgba(102, 126, 234, 0.15)',
        overflow: 'hidden'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: 8,
          minWidth: 0,
          flex: 1,
          overflow: 'hidden'
        }}>
          <div style={{
            background: 'rgba(255, 255, 255, 0.2)',
            borderRadius: '8px',
            padding: '6px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexShrink: 0
          }}>
            <TeamOutlined style={{ color: '#fff', fontSize: '18px' }} />
          </div>
          <div style={{
            minWidth: 0,
            flex: 1,
            overflow: 'hidden'
          }}>
            <div style={{
              fontSize: '18px',
              fontWeight: 700,
              color: '#fff',
              letterSpacing: '-0.3px',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              lineHeight: '1.2'
            }}>
              Farmers Directory
            </div>
            <div style={{
              fontSize: '11px',
              color: 'rgba(255, 255, 255, 0.8)',
              fontWeight: 400,
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              lineHeight: '1.2'
            }}>
              Milk Projection Management
            </div>
          </div>
        </div>
        <div style={{
          background: 'rgba(255, 255, 255, 0.15)',
          borderRadius: '6px',
          padding: '4px 8px',
          color: '#fff',
          fontSize: '11px',
          fontWeight: 500,
          flexShrink: 0,
          whiteSpace: 'nowrap',
          textAlign: 'center',
          minWidth: 'fit-content'
        }}>
          <div>{filteredFarmers.length} / {farmers.length}</div>
          {currentPage > 1 && (
            <div style={{ fontSize: '9px', opacity: 0.8 }}>
              ({displayedFarmers.length})
            </div>
          )}
        </div>
      </div>

      {/* Table Container */}
      <div style={{
        flex: 1,
        overflow: 'hidden',
        background: '#fff',
        display: 'flex',
        flexDirection: 'column',
        minHeight: 0
      }}>
        <Table
          columns={getColumns()}
          dataSource={displayedFarmers}
          rowKey="customer_id"
          loading={loading}
          pagination={false}
          onRow={(record) => ({
            onClick: () => navigate(`/farmer/${record.customer_id}/animals`, { state: { farmer: record } }),
            style: { cursor: 'pointer' },
          })}
          scroll={{
            x: 'max-content',
            y: 'calc(100vh - 60px)'
          }}
          size="small"
          style={{
            background: '#fff',
            height: '100%',
            width: '100%'
          }}
          rowClassName={(_, index) =>
            index % 2 === 0 ? 'table-row-even' : 'table-row-odd'
          }
          onScroll={handleScroll}
        />
      </div>

      <style>{`
        /* Reset any default margins/padding */
        * {
          box-sizing: border-box;
        }

        body, html {
          margin: 0 !important;
          padding: 0 !important;
          overflow: hidden !important;
          height: 100vh !important;
          width: 100% !important;
        }

        /* Prevent text overflow in headers */
        .ant-table-thead > tr > th .ant-table-column-title {
          overflow: hidden !important;
        }

        /* Table styling */
        .table-row-even:hover,
        .table-row-odd:hover {
          background-color: #e6f7ff !important;
        }

        .table-row-even {
          background-color: #fafafa !important;
        }

        .table-row-odd {
          background-color: #ffffff !important;
        }

        .ant-table-tbody > tr > td {
          padding: 8px 12px !important;
          border-bottom: 1px solid #f0f0f0 !important;
          vertical-align: middle !important;
        }

        .ant-table-thead > tr > th {
          background: #f5f5f5 !important;
          font-weight: 600 !important;
          padding: 8px 12px !important;
          border-bottom: 2px solid #e8e8e8 !important;
          position: sticky !important;
          top: 0 !important;
          z-index: 10 !important;
          vertical-align: top !important;
        }

        /* Optimize filter input spacing */
        .ant-table-thead .ant-input {
          margin-top: 4px !important;
        }

        .ant-table-thead .ant-input-affix-wrapper {
          padding: 2px 6px !important;
        }

        /* Full height table with proper scrolling */
        .ant-table-wrapper {
          height: 100% !important;
          overflow: hidden !important;
          display: flex !important;
          flex-direction: column !important;
        }

        .ant-table {
          height: 100% !important;
          display: flex !important;
          flex-direction: column !important;
        }

        .ant-table-container {
          flex: 1 !important;
          overflow: auto !important;
          min-height: 0 !important;
        }

        .ant-table-body {
          flex: 1 !important;
          overflow-x: auto !important;
          overflow-y: auto !important;
          min-width: 100% !important;
        }

        .ant-table-thead > tr > th,
        .ant-table-tbody > tr > td {
          white-space: nowrap !important;
        }

        /* Ensure table takes full width and allows horizontal scroll */
        .ant-table table {
          min-width: max-content !important;
          width: 100% !important;
        }

        /* Optimize column header layout */
        .ant-table-thead > tr > th > div {
          display: flex !important;
          flex-direction: column !important;
          gap: 4px !important;
          align-items: flex-start !important;
        }

        /* Responsive adjustments for farmer name circles */
        @media (max-width: 768px) {
          .farmer-name-circle {
            width: 24px !important;
            height: 24px !important;
            font-size: 10px !important;
          }

          .farmer-name-text {
            font-size: 11px !important;
          }

          .ant-table-tbody > tr > td {
            padding: 6px 8px !important;
          }

          .ant-table-thead > tr > th {
            padding: 6px 8px !important;
          }
        }

        @media (max-width: 480px) {
          .farmer-name-circle {
            width: 20px !important;
            height: 20px !important;
            font-size: 9px !important;
          }

          .farmer-name-text {
            font-size: 10px !important;
          }

          .ant-table-tbody > tr > td {
            padding: 4px 6px !important;
          }

          .ant-table-thead > tr > th {
            padding: 4px 6px !important;
          }
        }

        @media (min-width: 1200px) {
          .farmer-name-circle {
            width: 40px !important;
            height: 40px !important;
            font-size: 16px !important;
          }

          .farmer-name-text {
            font-size: 15px !important;
          }
        }

        @media (min-width: 1600px) {
          .farmer-name-circle {
            width: 44px !important;
            height: 44px !important;
            font-size: 18px !important;
          }

          .farmer-name-text {
            font-size: 16px !important;
          }
        }

        /* Remove pagination styling since we're using scroll */

        /* Horizontal scroll styling */
        .ant-table-body::-webkit-scrollbar {
          height: 8px;
          width: 8px;
        }

        .ant-table-body::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 4px;
        }

        .ant-table-body::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 4px;
        }

        .ant-table-body::-webkit-scrollbar-thumb:hover {
          background: #a8a8a8;
        }

        /* Loading overlay */
        .ant-spin-container {
          height: 100% !important;
        }

        /* Filter popup styling */
        .ant-dropdown {
          z-index: 1050 !important;
        }

        /* Smooth transitions */
        * {
          transition: all 0.2s ease;
        }
      `}</style>
    </div>
  );
};

export default MilkProjection; 
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { fetchFarmers } from './api';

const CARD_PER_PAGE = 20;

const cardStyle = {
  border: '1px solid #e3e6ea',
  borderRadius: 10,
  padding: '18px 20px',
  marginBottom: 14,
  background: '#f9fafb',
  minWidth: 260,
  maxWidth: 320,
  textAlign: 'left',
  boxShadow: '0 2px 8px rgba(60,72,88,0.06)',
  display: 'flex',
  flexDirection: 'column',
  gap: 10,
  transition: 'box-shadow 0.2s, transform 0.2s',
  cursor: 'pointer',
};
const cardHoverStyle = {
  boxShadow: '0 6px 18px rgba(60,72,88,0.13)',
  transform: 'translateY(-2px) scale(1.01)',
};
const labelStyle = { fontWeight: 600, color: '#3a3a3a', fontSize: 13, minWidth: 120, display: 'inline-block' };
const valueStyle = { fontWeight: 400, color: '#222', fontSize: 15 };

const filterBoxStyle = {
  display: 'flex',
  gap: 24,
  marginBottom: 28,
  alignItems: 'flex-end',
  flexWrap: 'wrap',
  background: '#f3f4f6',
  paddingBottom:  10
};
const filterGroupStyle = { display: 'flex', flexDirection: 'column', gap: 6 };
const filterLabelStyle = { fontWeight: 600, marginBottom: 4, color: '#444', fontSize: 14 };
const filterInputStyle = { padding: '7px 10px', borderRadius: 6, border: '1px solid #cfd8dc', fontSize: 15, minWidth: 160, background: '#fff' };

const spinnerStyle = {
  display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200, width: '100%'
};
const bounceStyle = {
  width: 40, height: 40, display: 'inline-block', position: 'relative'
};
const bounceCircleStyle = idx => ({
  width: 12, height: 12, background: '#7b8fa1', borderRadius: '50%', position: 'absolute', left: 14 * idx, animation: `bounce 1s ${idx * 0.15}s infinite cubic-bezier(.36,.07,.19,.97)`,
});

const MilkProjection = () => {
  const userToken = useSelector((state) => state.user.userToken);
  const navigate = useNavigate();
  const [farmers, setFarmers] = useState([]);
  const [villageFilter, setVillageFilter] = useState('');
  const [nameFilter, setNameFilter] = useState('');
  const [mobileFilter, setMobileFilter] = useState('');
  const [hoveredCard, setHoveredCard] = useState(null);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);

  useEffect(() => {
    const fetchData = async () => {
      if (!userToken) return;
      setLoading(true);
      try {
        const farmersData = await fetchFarmers(userToken);
        setFarmers(farmersData);
      } catch (error) {
        setFarmers([]);
      } finally {
        setTimeout(() => setLoading(false), 400);
      }
    };
    fetchData();
  }, [userToken]);

  // Get unique villages for dropdown
  const uniqueVillages = Array.from(new Set(farmers.map(f => f.village).filter(Boolean)));

  // Filtered farmers
  const filteredFarmers = farmers.filter(farmer => {
    const matchesVillage = villageFilter ? farmer.village === villageFilter : true;
    const matchesName = nameFilter ? (farmer.customer_name || '').toLowerCase().includes(nameFilter.toLowerCase()) : true;
    const matchesMobile = mobileFilter ? (farmer.mobile_number || '').replace(/\s|\-/g, '').includes(mobileFilter.replace(/\s|\-/g, '')) : true;
    return matchesVillage && matchesName && matchesMobile;
  });

  // Pagination logic
  const totalPages = Math.ceil(filteredFarmers.length / CARD_PER_PAGE) || 1;
  const paginatedFarmers = filteredFarmers.slice((page - 1) * CARD_PER_PAGE, page * CARD_PER_PAGE);
  const handlePageChange = (newPage) => {
    if (newPage < 1 || newPage > totalPages) return;
    setPage(newPage);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };
  useEffect(() => { setPage(1); }, [villageFilter, nameFilter, mobileFilter]);

  return (
    <div style={{ width: '100%', padding: 0, background: '#f3f4f6', minHeight: '100vh', boxSizing: 'border-box', position: 'relative' }}>
      <style>{`
        @keyframes bounce {
          0%, 80%, 100% { transform: scale(0.8); }
          40% { transform: scale(1.2); }
        }
        .milkproj-sticky-filter {
          position: sticky;
          top: 0;
          z-index: 110;
          background: #f6f7fa;
          box-shadow: 0 2px 8px rgba(60,72,88,0.04);
          padding-top: 12px;
          margin-bottom: 12px;
        }
      `}</style>
      <div className="milkproj-sticky-filter" style={filterBoxStyle}>
        <div style={filterGroupStyle}>
          <label style={filterLabelStyle}>Village</label>
          <select value={villageFilter} onChange={e => { setVillageFilter(e.target.value); setPage(1); }} style={filterInputStyle}>
            <option value=''>All</option>
            {uniqueVillages.map(village => (
              <option key={village} value={village}>{village}</option>
            ))}
          </select>
        </div>
        <div style={filterGroupStyle}>
          <label style={filterLabelStyle}>Name</label>
          <input
            type="text"
            value={nameFilter}
            onChange={e => { setNameFilter(e.target.value); setPage(1); }}
            placeholder="Search by name"
            style={filterInputStyle}
          />
        </div>
        <div style={filterGroupStyle}>
          <label style={filterLabelStyle}>Mobile Number</label>
          <input
            type="text"
            value={mobileFilter}
            onChange={e => { setMobileFilter(e.target.value); setPage(1); }}
            placeholder="Search by mobile"
            style={filterInputStyle}
          />
        </div>
      </div>
      {loading ? (
        <div style={spinnerStyle}>
          <div style={bounceStyle}>
            {[0, 1, 2].map(idx => (
              <div key={idx} style={bounceCircleStyle(idx)} />
            ))}
          </div>
        </div>
      ) : (
        <>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: 28, minHeight: 200,padding:10}}>
            {paginatedFarmers.length === 0 && (
              <div style={{ color: '#888', fontSize: 18, marginTop: 40 }}>No farmers found.</div>
            )}
            {paginatedFarmers.map((farmer) => (
              <div
                key={farmer.customer_id}
                style={{ ...cardStyle, ...(hoveredCard === farmer.customer_id ? cardHoverStyle : {}) }}
                onMouseEnter={() => setHoveredCard(farmer.customer_id)}
                onMouseLeave={() => setHoveredCard(null)}
                onClick={() => navigate(`/farmer/${farmer.customer_id}/animals`, { state: { farmer } })}
              >
                <div><span style={labelStyle}>Visual Id:</span> <span style={valueStyle}>{farmer.customer_visual_id || '-'}</span></div>
                <div><span style={labelStyle}>Name:</span> <span style={valueStyle}>{farmer.customer_name || '-'}</span></div>
                <div><span style={labelStyle}>Mobile:</span> <span style={valueStyle}>{farmer.mobile_number || '-'}</span></div>
                <div><span style={labelStyle}>Alt Contact Number:</span> <span style={valueStyle}>{farmer.alt_contact_number || '-'}</span></div>
                <div><span style={labelStyle}>Taluk:</span> <span style={valueStyle}>{farmer.taluk || '-'}</span></div>
                <div><span style={labelStyle}>Village:</span> <span style={valueStyle}>{farmer.village || '-'}</span></div>
                <div><span style={labelStyle}>LSS/Paravet:</span> <span style={valueStyle}>{farmer.staff_name || '-'}</span></div>
                <div><span style={labelStyle}>T&C:</span> <span style={valueStyle}>{farmer.terms || '-'}</span></div>
                <div><span style={labelStyle}>Cattle Pending Subscription:</span> <span style={valueStyle}>{farmer.active_unsubscribed_animals || 0}</span></div>
              </div>
            ))}
          </div>
            <div style={{marginTop: 100}}>
              
            </div>
        </>
      )}

      {totalPages > 1 && (
        <div
          style={{
            position: 'fixed',
            left: 0,
            bottom: 0,
            width: '100%',
            background: 'rgba(250,251,253,0.98)',
          }}
        >
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
            }}
          >
            <button
              style={{
                padding: '10px 20px',
                background: '#007bff',
                color: '#fff',
                border: 'none',
                borderRadius: 5,
                cursor: 'pointer',
              }}
              onClick={() => handlePageChange(page - 1)}
              disabled={page === 1}
            >
              Previous
            </button>
            <span style={{ margin: '0 10px', fontSize: 18, fontWeight: 600 }}>
              {page} / {totalPages}
            </span>
            <button
              style={{
                padding: '10px 20px',
                background: '#007bff',
                color: '#fff',
                border: 'none',
                borderRadius: 5,
                cursor: 'pointer',
              }}
              onClick={() => handlePageChange(page + 1)}
              disabled={page === totalPages}
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default MilkProjection; 
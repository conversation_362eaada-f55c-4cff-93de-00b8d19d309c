import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { Table, Input, Tag } from 'antd';
import { SearchOutlined, UserOutlined, PhoneOutlined, HomeOutlined, EnvironmentOutlined, IdcardOutlined, TeamOutlined } from '@ant-design/icons';
import { fetchFarmers } from './api';

const MilkProjection = () => {
  const userToken = useSelector((state) => state.user.userToken);
  const navigate = useNavigate();
  const [farmers, setFarmers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(50);
  const [searchFilters, setSearchFilters] = useState({
    name: '',
    mobile: '',
    village: '',
    taluk: '',
    staff: '',
    visualId: ''
  });

  // Debouncing hook
  const useDebounce = (value, delay) => {
    const [debouncedValue, setDebouncedValue] = useState(value);

    useEffect(() => {
      const handler = setTimeout(() => {
        setDebouncedValue(value);
      }, delay);

      return () => {
        clearTimeout(handler);
      };
    }, [value, delay]);

    return debouncedValue;
  };

  const debouncedSearchFilters = useDebounce(searchFilters, 300);

  // Handle filter changes
  const handleFilterChange = (field, value) => {
    setSearchFilters(prev => ({
      ...prev,
      [field]: value
    }));
    setCurrentPage(1); // Reset to first page when filtering
  };

  // Table columns configuration with integrated filters
  const getColumns = () => [
    {
      title: (
        <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
            <IdcardOutlined style={{ color: '#667eea' }} />
            <span>Visual ID</span>
          </div>
          <Input
            placeholder="Filter ID"
            value={searchFilters.visualId}
            onChange={(e) => handleFilterChange('visualId', e.target.value)}
            size="small"
            style={{ fontSize: '12px' }}
            prefix={<SearchOutlined style={{ color: '#bfbfbf' }} />}
          />
        </div>
      ),
      dataIndex: 'customer_visual_id',
      key: 'customer_visual_id',
      width: 160,
      minWidth: 160,
      render: (text) => (
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: 8,
          padding: '4px 0'
        }}>
          <div style={{
            background: 'linear-gradient(135deg, #667eea, #764ba2)',
            color: '#fff',
            borderRadius: '6px',
            padding: '4px 8px',
            fontSize: '12px',
            fontWeight: 600,
            letterSpacing: '0.5px'
          }}>
            {text || 'N/A'}
          </div>
        </div>
      ),
    },
    {
      title: (
        <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
            <UserOutlined style={{ color: '#52c41a' }} />
            <span>Farmer Name</span>
          </div>
          <Input
            placeholder="Filter name"
            value={searchFilters.name}
            onChange={(e) => handleFilterChange('name', e.target.value)}
            size="small"
            style={{ fontSize: '12px' }}
            prefix={<SearchOutlined style={{ color: '#bfbfbf' }} />}
          />
        </div>
      ),
      dataIndex: 'customer_name',
      key: 'customer_name',
      width: 220,
      minWidth: 220,
      render: (text) => (
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: 10,
          padding: '4px 0'
        }}>
          <div style={{
            width: '36px',
            height: '36px',
            borderRadius: '50%',
            background: 'linear-gradient(135deg, #52c41a, #73d13d)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#fff',
            fontWeight: 600,
            fontSize: '14px'
          }}>
            {text ? text.charAt(0).toUpperCase() : 'F'}
          </div>
          <div>
            <div style={{
              fontWeight: 600,
              color: '#262626',
              fontSize: '14px'
            }}>
              {text || 'Unknown'}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: (
        <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
            <PhoneOutlined style={{ color: '#fa8c16' }} />
            <span>Mobile</span>
          </div>
          <Input
            placeholder="Filter mobile"
            value={searchFilters.mobile}
            onChange={(e) => handleFilterChange('mobile', e.target.value)}
            size="small"
            style={{ fontSize: '12px' }}
            prefix={<SearchOutlined style={{ color: '#bfbfbf' }} />}
          />
        </div>
      ),
      dataIndex: 'mobile_number',
      key: 'mobile_number',
      width: 170,
      minWidth: 170,
      render: (text) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
          <PhoneOutlined style={{ color: '#fa8c16' }} />
          <span style={{ fontWeight: 500 }}>{text || '-'}</span>
        </div>
      ),
    },
    {
      title: 'Alt Contact',
      dataIndex: 'alt_contact_number',
      key: 'alt_contact_number',
      width: 140,
      minWidth: 140,
      render: (text) => (
        <span style={{ color: '#595959' }}>{text || '-'}</span>
      ),
    },
    {
      title: (
        <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
            <HomeOutlined style={{ color: '#722ed1' }} />
            <span>Village</span>
          </div>
          <Input
            placeholder="Filter village"
            value={searchFilters.village}
            onChange={(e) => handleFilterChange('village', e.target.value)}
            size="small"
            style={{ fontSize: '12px' }}
            prefix={<SearchOutlined style={{ color: '#bfbfbf' }} />}
          />
        </div>
      ),
      dataIndex: 'village',
      key: 'village',
      width: 160,
      minWidth: 160,
      render: (text) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
          <HomeOutlined style={{ color: '#722ed1' }} />
          <span style={{ fontWeight: 500 }}>{text || '-'}</span>
        </div>
      ),
    },
    {
      title: (
        <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
            <EnvironmentOutlined style={{ color: '#eb2f96' }} />
            <span>Taluk</span>
          </div>
          <Input
            placeholder="Filter taluk"
            value={searchFilters.taluk}
            onChange={(e) => handleFilterChange('taluk', e.target.value)}
            size="small"
            style={{ fontSize: '12px' }}
            prefix={<SearchOutlined style={{ color: '#bfbfbf' }} />}
          />
        </div>
      ),
      dataIndex: 'taluk',
      key: 'taluk',
      width: 160,
      minWidth: 160,
      render: (text) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
          <EnvironmentOutlined style={{ color: '#eb2f96' }} />
          <span style={{ fontWeight: 500 }}>{text || '-'}</span>
        </div>
      ),
    },
    {
      title: (
        <div style={{ display: 'flex', flexDirection: 'column', gap: 4 }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
            <TeamOutlined style={{ color: '#13c2c2' }} />
            <span>LSS/Paravet</span>
          </div>
          <Input
            placeholder="Filter staff"
            value={searchFilters.staff}
            onChange={(e) => handleFilterChange('staff', e.target.value)}
            size="small"
            style={{ fontSize: '12px' }}
            prefix={<SearchOutlined style={{ color: '#bfbfbf' }} />}
          />
        </div>
      ),
      dataIndex: 'staff_name',
      key: 'staff_name',
      width: 180,
      minWidth: 180,
      render: (text) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
          <TeamOutlined style={{ color: '#13c2c2' }} />
          <span style={{ fontWeight: 500 }}>{text || '-'}</span>
        </div>
      ),
    },
    {
      title: 'T&C',
      dataIndex: 'terms',
      key: 'terms',
      width: 120,
      minWidth: 120,
      render: (text) => (
        <Tag color={text ? 'green' : 'default'}>{text || '-'}</Tag>
      ),
    },
    {
      title: 'Pending Subscription',
      dataIndex: 'active_unsubscribed_animals',
      key: 'active_unsubscribed_animals',
      width: 180,
      minWidth: 180,
      render: (text) => (
        <Tag color={text > 0 ? 'orange' : 'green'}>{text || 0}</Tag>
      ),
    },
  ];

  useEffect(() => {
    const fetchData = async () => {
      if (!userToken) return;
      setLoading(true);
      try {
        const farmersData = await fetchFarmers(userToken);
        setFarmers(farmersData);
      } catch (error) {
        setFarmers([]);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [userToken]);

  // Filter farmers based on search filters
  const filteredFarmers = farmers.filter(farmer => {
    const matchesVisualId = !debouncedSearchFilters.visualId ||
      (farmer.customer_visual_id || '').toLowerCase().includes(debouncedSearchFilters.visualId.toLowerCase());

    const matchesName = !debouncedSearchFilters.name ||
      (farmer.customer_name || '').toLowerCase().includes(debouncedSearchFilters.name.toLowerCase());

    const matchesMobile = !debouncedSearchFilters.mobile ||
      (farmer.mobile_number || '').replace(/\s|\-/g, '').includes(debouncedSearchFilters.mobile.replace(/\s|\-/g, ''));

    const matchesVillage = !debouncedSearchFilters.village ||
      (farmer.village || '').toLowerCase().includes(debouncedSearchFilters.village.toLowerCase());

    const matchesTaluk = !debouncedSearchFilters.taluk ||
      (farmer.taluk || '').toLowerCase().includes(debouncedSearchFilters.taluk.toLowerCase());

    const matchesStaff = !debouncedSearchFilters.staff ||
      (farmer.staff_name || '').toLowerCase().includes(debouncedSearchFilters.staff.toLowerCase());

    return matchesVisualId && matchesName && matchesMobile && matchesVillage && matchesTaluk && matchesStaff;
  });

  // Pagination logic
  const totalPages = Math.ceil(filteredFarmers.length / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedFarmers = filteredFarmers.slice(startIndex, endIndex);

  // Handle scroll to load more
  const handleScroll = (e) => {
    const { target } = e;
    const { scrollTop, scrollHeight, clientHeight } = target;

    // Load more when scrolled to bottom
    if (scrollHeight - scrollTop === clientHeight && currentPage < totalPages) {
      setCurrentPage(prev => prev + 1);
    }
  };

  // Get farmers to display (with infinite scroll pagination)
  const displayedFarmers = filteredFarmers.slice(0, currentPage * pageSize);

  return (
    <div style={{
      width: '100vw',
      height: '100vh',
      padding: 0,
      margin: 0,
      background: '#fff',
      boxSizing: 'border-box',
      overflow: 'hidden',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Header */}
      <div style={{
        padding: '16px 24px',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        borderBottom: 'none',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        gap: 12,
        flexShrink: 0,
        minHeight: '64px',
        boxSizing: 'border-box',
        boxShadow: '0 4px 20px rgba(102, 126, 234, 0.15)',
        overflow: 'hidden'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: 12,
          minWidth: 0,
          flex: 1
        }}>
          <div style={{
            background: 'rgba(255, 255, 255, 0.2)',
            borderRadius: '12px',
            padding: '8px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexShrink: 0
          }}>
            <TeamOutlined style={{ color: '#fff', fontSize: '20px' }} />
          </div>
          <div style={{ minWidth: 0, flex: 1 }}>
            <div style={{
              fontSize: '20px',
              fontWeight: 700,
              color: '#fff',
              letterSpacing: '-0.5px',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis'
            }}>
              Farmers Directory
            </div>
            <div style={{
              fontSize: '12px',
              color: 'rgba(255, 255, 255, 0.8)',
              fontWeight: 400,
              marginTop: '2px',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis'
            }}>
              Milk Projection Management
            </div>
          </div>
        </div>
        <div style={{
          background: 'rgba(255, 255, 255, 0.15)',
          borderRadius: '8px',
          padding: '6px 12px',
          color: '#fff',
          fontSize: '12px',
          fontWeight: 500,
          flexShrink: 0,
          whiteSpace: 'nowrap'
        }}>
          {filteredFarmers.length} of {farmers.length}
          {currentPage > 1 && (
            <div style={{ fontSize: '10px', opacity: 0.8, marginTop: '2px' }}>
              Showing {displayedFarmers.length}
            </div>
          )}
        </div>
      </div>

      {/* Table Container */}
      <div style={{
        flex: 1,
        overflow: 'hidden',
        background: '#fff'
      }}>
        <Table
          columns={getColumns()}
          dataSource={displayedFarmers}
          rowKey="customer_id"
          loading={loading}
          pagination={false}
          onRow={(record) => ({
            onClick: () => navigate(`/farmer/${record.customer_id}/animals`, { state: { farmer: record } }),
            style: { cursor: 'pointer' },
          })}
          scroll={{
            x: 'max-content',
            y: 'calc(100vh - 144px)'
          }}
          size="middle"
          style={{
            background: '#fff',
            height: '100%',
          }}
          rowClassName={(_, index) =>
            index % 2 === 0 ? 'table-row-even' : 'table-row-odd'
          }
          onScroll={handleScroll}
        />
      </div>

      <style>{`
        /* Reset any default margins/padding */
        * {
          box-sizing: border-box;
        }

        body, html {
          margin: 0 !important;
          padding: 0 !important;
          overflow: hidden !important;
          height: 100vh !important;
        }

        /* Table styling */
        .table-row-even:hover,
        .table-row-odd:hover {
          background-color: #e6f7ff !important;
        }

        .table-row-even {
          background-color: #fafafa !important;
        }

        .table-row-odd {
          background-color: #ffffff !important;
        }

        .ant-table-tbody > tr > td {
          padding: 12px 16px !important;
          border-bottom: 1px solid #f0f0f0 !important;
        }

        .ant-table-thead > tr > th {
          background: #f5f5f5 !important;
          font-weight: 600 !important;
          padding: 16px !important;
          border-bottom: 2px solid #e8e8e8 !important;
          position: sticky !important;
          top: 0 !important;
          z-index: 10 !important;
        }

        /* Full height table with proper scrolling */
        .ant-table-wrapper {
          height: 100% !important;
          overflow: hidden !important;
        }

        .ant-table {
          height: 100% !important;
        }

        .ant-table-container {
          height: calc(100% - 56px) !important;
          overflow: auto !important;
        }

        .ant-table-body {
          height: 100% !important;
          overflow-x: auto !important;
          overflow-y: auto !important;
          min-width: 100% !important;
        }

        .ant-table-thead > tr > th,
        .ant-table-tbody > tr > td {
          white-space: nowrap !important;
        }

        /* Ensure table takes full width and allows horizontal scroll */
        .ant-table table {
          min-width: max-content !important;
        }

        /* Remove pagination styling since we're using scroll */

        /* Horizontal scroll styling */
        .ant-table-body::-webkit-scrollbar {
          height: 8px;
          width: 8px;
        }

        .ant-table-body::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 4px;
        }

        .ant-table-body::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 4px;
        }

        .ant-table-body::-webkit-scrollbar-thumb:hover {
          background: #a8a8a8;
        }

        /* Loading overlay */
        .ant-spin-container {
          height: 100% !important;
        }

        /* Filter popup styling */
        .ant-dropdown {
          z-index: 1050 !important;
        }

        /* Smooth transitions */
        * {
          transition: all 0.2s ease;
        }
      `}</style>
    </div>
  );
};

export default MilkProjection; 
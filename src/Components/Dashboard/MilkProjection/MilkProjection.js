import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { Table, Input, Select, Card, Space, Button, Tag, Spin } from 'antd';
import { SearchOutlined, UserOutlined, PhoneOutlined, HomeOutlined, EnvironmentOutlined, IdcardOutlined, TeamOutlined } from '@ant-design/icons';
import { fetchFarmers } from './api';

const { Option } = Select;

const MilkProjection = () => {
  const userToken = useSelector((state) => state.user.userToken);
  const navigate = useNavigate();
  const [farmers, setFarmers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // Table columns configuration
  const getColumns = () => [
    {
      title: 'Visual ID',
      dataIndex: 'customer_visual_id',
      key: 'customer_visual_id',
      width: 120,
      render: (text) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
          <IdcardOutlined style={{ color: '#1890ff' }} />
          <span style={{ fontWeight: 500 }}>{text || '-'}</span>
        </div>
      ),
    },
    {
      title: 'Name',
      dataIndex: 'customer_name',
      key: 'customer_name',
      width: 150,
      render: (text) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
          <UserOutlined style={{ color: '#52c41a' }} />
          <span style={{ fontWeight: 500 }}>{text || '-'}</span>
        </div>
      ),
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ padding: 8 }}>
          <Input
            placeholder="Search name"
            value={selectedKeys[0]}
            onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => confirm()}
            style={{ width: 188, marginBottom: 8, display: 'block' }}
          />
          <Space>
            <Button
              type="primary"
              onClick={() => confirm()}
              icon={<SearchOutlined />}
              size="small"
              style={{ width: 90 }}
            >
              Search
            </Button>
            <Button onClick={() => clearFilters()} size="small" style={{ width: 90 }}>
              Reset
            </Button>
          </Space>
        </div>
      ),
      filterIcon: (filtered) => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
      onFilter: (value, record) =>
        record.customer_name?.toLowerCase().includes(value.toLowerCase()),
    },
    {
      title: 'Mobile',
      dataIndex: 'mobile_number',
      key: 'mobile_number',
      width: 130,
      render: (text) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
          <PhoneOutlined style={{ color: '#fa8c16' }} />
          <span>{text || '-'}</span>
        </div>
      ),
    },
    {
      title: 'Alt Contact',
      dataIndex: 'alt_contact_number',
      key: 'alt_contact_number',
      width: 130,
      render: (text) => (
        <span>{text || '-'}</span>
      ),
    },
    {
      title: 'Village',
      dataIndex: 'village',
      key: 'village',
      width: 120,
      render: (text) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
          <HomeOutlined style={{ color: '#722ed1' }} />
          <span>{text || '-'}</span>
        </div>
      ),
      filters: Array.from(new Set(farmers.map(f => f.village).filter(Boolean))).map(village => ({
        text: village,
        value: village,
      })),
      onFilter: (value, record) => record.village === value,
    },
    {
      title: 'Taluk',
      dataIndex: 'taluk',
      key: 'taluk',
      width: 120,
      render: (text) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
          <EnvironmentOutlined style={{ color: '#eb2f96' }} />
          <span>{text || '-'}</span>
        </div>
      ),
    },
    {
      title: 'LSS/Paravet',
      dataIndex: 'staff_name',
      key: 'staff_name',
      width: 150,
      render: (text) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
          <TeamOutlined style={{ color: '#13c2c2' }} />
          <span>{text || '-'}</span>
        </div>
      ),
    },
    {
      title: 'T&C',
      dataIndex: 'terms',
      key: 'terms',
      width: 100,
      render: (text) => (
        <Tag color={text ? 'green' : 'default'}>{text || '-'}</Tag>
      ),
    },
    {
      title: 'Pending Subscription',
      dataIndex: 'active_unsubscribed_animals',
      key: 'active_unsubscribed_animals',
      width: 150,
      render: (text) => (
        <Tag color={text > 0 ? 'orange' : 'green'}>{text || 0}</Tag>
      ),
    },
  ];

  useEffect(() => {
    const fetchData = async () => {
      if (!userToken) return;
      setLoading(true);
      try {
        const farmersData = await fetchFarmers(userToken);
        setFarmers(farmersData);
        setPagination(prev => ({ ...prev, total: farmersData.length }));
      } catch (error) {
        setFarmers([]);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [userToken]);

  const handleTableChange = (paginationConfig) => {
    setPagination(paginationConfig);
  };

  return (
    <div style={{
      width: '100vw',
      height: '100vh',
      padding: 0,
      margin: 0,
      background: '#fff',
      boxSizing: 'border-box',
      overflow: 'hidden',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Header */}
      <div style={{
        padding: '16px 24px',
        background: '#fff',
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        alignItems: 'center',
        gap: 8,
        flexShrink: 0,
        height: '64px',
        boxSizing: 'border-box'
      }}>
        <TeamOutlined style={{ color: '#1890ff', fontSize: '20px' }} />
        <span style={{ fontSize: '20px', fontWeight: 600, color: '#262626' }}>Farmers - Milk Projection</span>
      </div>

      {/* Table Container */}
      <div style={{
        flex: 1,
        overflow: 'hidden',
        background: '#fff'
      }}>
        <Table
          columns={getColumns()}
          dataSource={farmers}
          rowKey="customer_id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} of ${total} farmers`,
            pageSizeOptions: ['10', '20', '50', '100'],
            position: ['bottomCenter'],
            style: { padding: '16px 24px' }
          }}
          onChange={handleTableChange}
          onRow={(record) => ({
            onClick: () => navigate(`/farmer/${record.customer_id}/animals`, { state: { farmer: record } }),
            style: { cursor: 'pointer' },
          })}
          scroll={{
            x: 1400,
            y: 'calc(100vh - 128px)'
          }}
          size="middle"
          style={{
            background: '#fff',
            height: '100%',
          }}
          rowClassName={(_, index) =>
            index % 2 === 0 ? 'table-row-even' : 'table-row-odd'
          }
        />
      </div>

      <style>{`
        /* Reset any default margins/padding */
        * {
          box-sizing: border-box;
        }

        body, html {
          margin: 0 !important;
          padding: 0 !important;
          overflow: hidden !important;
          height: 100vh !important;
        }

        /* Table styling */
        .table-row-even:hover,
        .table-row-odd:hover {
          background-color: #e6f7ff !important;
        }

        .table-row-even {
          background-color: #fafafa !important;
        }

        .table-row-odd {
          background-color: #ffffff !important;
        }

        .ant-table-tbody > tr > td {
          padding: 12px 16px !important;
          border-bottom: 1px solid #f0f0f0 !important;
        }

        .ant-table-thead > tr > th {
          background: #f5f5f5 !important;
          font-weight: 600 !important;
          padding: 16px !important;
          border-bottom: 2px solid #e8e8e8 !important;
          position: sticky !important;
          top: 0 !important;
          z-index: 10 !important;
        }

        /* Full height table */
        .ant-table-wrapper {
          height: 100% !important;
          overflow: hidden !important;
        }

        .ant-table {
          height: 100% !important;
        }

        .ant-table-container {
          height: calc(100% - 56px) !important;
          overflow: auto !important;
        }

        .ant-table-body {
          height: 100% !important;
          overflow-x: auto !important;
          overflow-y: auto !important;
        }

        /* Pagination styling */
        .ant-pagination {
          margin: 0 !important;
          padding: 16px 24px !important;
          background: #fafafa !important;
          border-top: 1px solid #f0f0f0 !important;
        }

        /* Horizontal scroll styling */
        .ant-table-body::-webkit-scrollbar {
          height: 8px;
          width: 8px;
        }

        .ant-table-body::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 4px;
        }

        .ant-table-body::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 4px;
        }

        .ant-table-body::-webkit-scrollbar-thumb:hover {
          background: #a8a8a8;
        }

        /* Loading overlay */
        .ant-spin-container {
          height: 100% !important;
        }
      `}</style>
    </div>
  );
};

export default MilkProjection; 
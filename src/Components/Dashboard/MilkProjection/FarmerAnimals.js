"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useLocation } from "react-router-dom"
import { Spin, Alert, Table, Select, InputNumber, Button, Space, Image, Tag, Card, Row, Col, Statistic } from "antd"
import {
  UserOutlined,
  PhoneOutlined,
  HomeOutlined,
  EnvironmentOutlined,
  IdcardOutlined,
  EditOutlined,
  SaveOutlined,
  CloseOutlined,
  <PERSON><PERSON>hartOutlined,
  TeamOutlined,
} from "@ant-design/icons"
import { useSelector } from "react-redux"
import { fetchAnimalsForFarmer } from "./api"
import { Line } from "react-chartjs-2"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js"
import annotationPlugin from "chartjs-plugin-annotation"
import { getPlotPoints } from "./plot"

const { Option } = Select

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, annotationPlugin)

const placeholderImg = "https://cdn-icons-png.flaticon.com/512/616/616408.png"

// Helper to get a displayable value from a field
function getDisplayValue(value) {
  if (value === null || value === undefined || value === "") return ""
  if (typeof value === "object") {
    if (typeof value.en === "string") return value.en
    if (typeof value.mr === "string") return value.mr
    if (typeof value.value === "string") return value.value
    if (typeof value.label === "string") return value.label
    if (Array.isArray(value)) return value.map((v) => String(v)).join(", ")
    const firstString = Object.values(value).find((v) => typeof v === "string")
    return firstString || ""
  }
  return String(value)
}

const getS3ImageUrl = async (animal_document_id, token) => {
  try {
    const response = await fetch(`/api/document?document_id=${animal_document_id}`, {
      headers: { token: token },
    })
    if (!response.ok) return null
    const data = await response.json()
    if (data?.data?.url) return data.data.url
    if (data?.data?.doc_key) return data.data.doc_key
    return null
  } catch {
    return null
  }
}

const CATTLE_TYPES = [
  { value: "cow", label: "Cow" },
  { value: "buffalo", label: "Buffalo" },
]

const BREEDS = [
  { value: "HF", label: "HF" },
  { value: "Jersey", label: "Jersey" },
  { value: "Other", label: "Other" },
]

const CALVINGS = [1, 2, 3, 4, 5, 6, 7]

const BCS = [
  { value: "1.0", label: "1.0 - Emaciated" },
  { value: "1.5", label: "1.5 - Very Thin" },
  { value: "2.0", label: "2.0 - Thin" },
  { value: "2.5", label: "2.5 - Slightly Thin" },
  { value: "3.0", label: "3.0 - Good" },
  { value: "3.5", label: "3.5 - Above Average" },
  { value: "4.0", label: "4.0 - Fat" },
  { value: "4.5", label: "4.5 - Very Fat" },
  { value: "5.0", label: "5.0 - Obese" },
]

function getInitialEditable(animal) {
  let breed = getDisplayValue(animal.animal_breed_1)
  if (breed !== "HF" && breed !== "Jersey") {
    breed = "HF"
  }
  return {
    cattleType: getDisplayValue(animal.animal_type) || "cow",
    breed,
    calvingNumber: getDisplayValue(animal.calving_number) || 1,
    age: getDisplayValue(animal.animal_age_1) || "",
    dataCollectionDate: "",
    monthsSinceCalving: getDisplayValue(animal.months_since_calving) || "",
    monthsSincePregnancy: getDisplayValue(animal.months_since_pregnancy) || "",
    bodyConditionScore: getDisplayValue(animal.body_condition_score) || "3.0",
    milkProduction: getDisplayValue(animal.milk_production) || "",
    milkProductionDate: "",
  }
}

const month_name = {
  1: "January",
  2: "February",
  3: "March",
  4: "April",
  5: "May",
  6: "June",
  7: "July",
  8: "August",
  9: "September",
  10: "October",
  11: "November",
  12: "December",
}

const FarmerAnimals = () => {
  const { farmerId } = useParams()
  const location = useLocation()
  const farmer = location.state?.farmer
  const userToken = useSelector((state) => state.user.userToken)

  const [animals, setAnimals] = useState([])
  const [selected, setSelected] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [animalImages, setAnimalImages] = useState({})
  const [editable, setEditable] = useState({})
  const [editingKey, setEditingKey] = useState("")
  const [chartData, setChartData] = useState(null)
  const [chartOptions, setChartOptions] = useState(null)

  useEffect(() => {
    const fetchAnimals = async () => {
      if (!userToken || !farmerId) return
      setLoading(true)
      setError(null)
      try {
        const animalsData = await fetchAnimalsForFarmer(userToken, farmerId)
        setAnimals(animalsData)
      } catch (err) {
        setError("Failed to fetch animals.")
        setAnimals([])
      } finally {
        setLoading(false)
      }
    }
    fetchAnimals()
  }, [userToken, farmerId])

  useEffect(() => {
    const fetchImages = async () => {
      if (!userToken) return
      const promises = animals.map(async (animal) => {
        if (animal.animal_document_id) {
          const url = await getS3ImageUrl(animal.animal_document_id, userToken.accessToken)
          return { animal_id: animal.animal_id, url }
        }
        return null
      })
      const results = await Promise.all(promises)
      const imageMap = {}
      results.forEach((res) => {
        if (res && res.url) imageMap[res.animal_id] = res.url
      })
      setAnimalImages(imageMap)
    }
    if (animals.length > 0) fetchImages()
  }, [animals, userToken])

  useEffect(() => {
    if (animals.length > 0) {
      const initial = {}
      animals.forEach((animal) => {
        initial[animal.animal_id] = getInitialEditable(animal)
      })
      setEditable(initial)
    }
  }, [animals])

  const handleEditChange = (animalId, field, value) => {
    setEditable((prev) => ({
      ...prev,
      [animalId]: {
        ...prev[animalId],
        [field]: value,
      },
    }))
  }

  const isEditing = (record) => record.animal_id === editingKey
  const edit = (record) => setEditingKey(record.animal_id)
  const cancel = () => setEditingKey("")
  const save = async () => {
    try {
      setEditingKey("")
      // API call to save data would go here
    } catch (errInfo) {
      console.log("Validate Failed:", errInfo)
    }
  }

  // Farmer table configuration
  const farmerTableColumns = [
    {
      title: "Field",
      dataIndex: "field",
      key: "field",
      width: 150,
      render: (text, record) => (
        <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
          {record.icon}
          <span style={{ fontWeight: 600, color: "#1890ff" }}>{text}</span>
        </div>
      ),
    },
    {
      title: "Value",
      dataIndex: "value",
      key: "value",
      render: (text) => <span style={{ fontWeight: 500, color: "#23272f" }}>{text}</span>,
    },
  ]

  const getFarmerTableData = (farmer) => [
    {
      key: "name",
      field: "Name",
      value: farmer.customer_name || "-",
      icon: <UserOutlined style={{ color: "#1890ff" }} />,
    },
    {
      key: "mobile",
      field: "Mobile",
      value: farmer.mobile_number || "-",
      icon: <PhoneOutlined style={{ color: "#52c41a" }} />,
    },
    {
      key: "village",
      field: "Village",
      value: farmer.village || "-",
      icon: <HomeOutlined style={{ color: "#fa8c16" }} />,
    },
    {
      key: "taluk",
      field: "Taluk",
      value: farmer.taluk || "-",
      icon: <EnvironmentOutlined style={{ color: "#722ed1" }} />,
    },
    {
      key: "farmerId",
      field: "Farmer ID",
      value: farmer.customer_visual_id || "-",
      icon: <IdcardOutlined style={{ color: "#eb2f96" }} />,
    },
  ]

  // Animal table columns configuration
  const getAnimalColumns = () => [
    {
      title: "Image",
      dataIndex: "image",
      key: "image",
      width: 80,
      render: (_, record) => {
        let imgUrl = animalImages[record.animal_id]
        if (!imgUrl && record.document_information?.url) {
          imgUrl = `https://ahs3.krushal.in/${record.document_information.url}`
        }
        if (!imgUrl) imgUrl = placeholderImg

        return (
          <Image
            width={60}
            height={60}
            src={imgUrl || "/placeholder.svg"}
            style={{ borderRadius: "8px", objectFit: "cover" }}
            fallback={placeholderImg}
          />
        )
      },
    },
    {
      title: "Animal Info",
      dataIndex: "info",
      key: "info",
      width: 200,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 600, fontSize: "14px", marginBottom: 4 }}>
            {getDisplayValue(record.animal_name) || getDisplayValue(record.animal_visual_id) || record.animal_id}
          </div>
          <div style={{ color: "#666", fontSize: "12px" }}>{getDisplayValue(record.animal_type) || "-"}</div>
          <div style={{ color: "#666", fontSize: "12px" }}>Age: {getDisplayValue(record.animal_age_1) || "-"}</div>
        </div>
      ),
    },
    {
      title: "Breed",
      dataIndex: "breed",
      key: "breed",
      width: 120,
      render: (_, record) => {
        const editing = isEditing(record)
        return editing ? (
          <Select
            value={editable[record.animal_id]?.breed || "HF"}
            onChange={(value) => handleEditChange(record.animal_id, "breed", value)}
            style={{ width: "100%" }}
            size="small"
          >
            {BREEDS.map((opt) => (
              <Option key={opt.value} value={opt.value}>
                {opt.label}
              </Option>
            ))}
          </Select>
        ) : (
          <Tag color="blue">{editable[record.animal_id]?.breed || "HF"}</Tag>
        )
      },
    },
    {
      title: "Cattle Type",
      dataIndex: "cattleType",
      key: "cattleType",
      width: 120,
      render: (_, record) => {
        const editing = isEditing(record)
        return editing ? (
          <Select
            value={editable[record.animal_id]?.cattleType || "cow"}
            onChange={(value) => handleEditChange(record.animal_id, "cattleType", value)}
            style={{ width: "100%" }}
            size="small"
          >
            {CATTLE_TYPES.map((opt) => (
              <Option key={opt.value} value={opt.value}>
                {opt.label}
              </Option>
            ))}
          </Select>
        ) : (
          <Tag color="green">{editable[record.animal_id]?.cattleType || "cow"}</Tag>
        )
      },
    },
    {
      title: "Age",
      dataIndex: "age",
      key: "age",
      width: 80,
      render: (_, record) => {
        const editing = isEditing(record)
        return editing ? (
          <InputNumber
            min={2}
            max={15}
            value={editable[record.animal_id]?.age || ""}
            onChange={(value) => handleEditChange(record.animal_id, "age", value)}
            style={{ width: "100%" }}
            size="small"
          />
        ) : (
          <span>{editable[record.animal_id]?.age || "-"}</span>
        )
      },
    },
    {
      title: "Calving #",
      dataIndex: "calvingNumber",
      key: "calvingNumber",
      width: 100,
      render: (_, record) => {
        const editing = isEditing(record)
        return editing ? (
          <Select
            value={editable[record.animal_id]?.calvingNumber || 1}
            onChange={(value) => handleEditChange(record.animal_id, "calvingNumber", value)}
            style={{ width: "100%" }}
            size="small"
          >
            {CALVINGS.map((num) => (
              <Option key={num} value={num}>
                {num}
              </Option>
            ))}
          </Select>
        ) : (
          <span>{editable[record.animal_id]?.calvingNumber || 1}</span>
        )
      },
    },
    {
      title: "BCS",
      dataIndex: "bodyConditionScore",
      key: "bodyConditionScore",
      width: 120,
      render: (_, record) => {
        const editing = isEditing(record)
        const bcsValue = editable[record.animal_id]?.bodyConditionScore || "3.0"
        const bcsLabel = BCS.find((b) => b.value === bcsValue)?.label || bcsValue

        return editing ? (
          <Select
            value={bcsValue}
            onChange={(value) => handleEditChange(record.animal_id, "bodyConditionScore", value)}
            style={{ width: "100%" }}
            size="small"
          >
            {BCS.map((opt) => (
              <Option key={opt.value} value={opt.value}>
                {opt.label}
              </Option>
            ))}
          </Select>
        ) : (
          <Tag color={Number.parseFloat(bcsValue) >= 3.0 ? "green" : "orange"}>{bcsLabel}</Tag>
        )
      },
    },
    {
      title: "Milk Production (L)",
      dataIndex: "milkProduction",
      key: "milkProduction",
      width: 150,
      render: (_, record) => {
        const editing = isEditing(record)
        return editing ? (
          <InputNumber
            min={0}
            step={0.1}
            value={editable[record.animal_id]?.milkProduction || ""}
            onChange={(value) => handleEditChange(record.animal_id, "milkProduction", value)}
            style={{ width: "100%" }}
            size="small"
          />
        ) : (
          <span>{editable[record.animal_id]?.milkProduction || "-"} L</span>
        )
      },
    },
    {
      title: "Months Since Calving",
      dataIndex: "monthsSinceCalving",
      key: "monthsSinceCalving",
      width: 150,
      render: (_, record) => {
        const editing = isEditing(record)
        return editing ? (
          <InputNumber
            min={0}
            max={120}
            value={editable[record.animal_id]?.monthsSinceCalving || ""}
            onChange={(value) => handleEditChange(record.animal_id, "monthsSinceCalving", value)}
            style={{ width: "100%" }}
            size="small"
            placeholder="Months"
          />
        ) : (
          <span>{editable[record.animal_id]?.monthsSinceCalving || "-"}</span>
        )
      },
    },
    {
      title: "Months Since Pregnancy",
      dataIndex: "monthsSincePregnancy",
      key: "monthsSincePregnancy",
      width: 160,
      render: (_, record) => {
        const editing = isEditing(record)
        return editing ? (
          <InputNumber
            min={0}
            max={120}
            value={editable[record.animal_id]?.monthsSincePregnancy || ""}
            onChange={(value) => handleEditChange(record.animal_id, "monthsSincePregnancy", value)}
            style={{ width: "100%" }}
            size="small"
            placeholder="Months"
          />
        ) : (
          <span>{editable[record.animal_id]?.monthsSincePregnancy || "-"}</span>
        )
      },
    },
    {
      title: "Data Collection Date",
      dataIndex: "dataCollectionDate",
      key: "dataCollectionDate",
      width: 180,
      render: (_, record) => {
        const editing = isEditing(record)
        return editing ? (
          <input
            type="datetime-local"
            value={editable[record.animal_id]?.dataCollectionDate || ""}
            onChange={(e) => handleEditChange(record.animal_id, "dataCollectionDate", e.target.value)}
            style={{
              width: "100%",
              padding: "4px 8px",
              border: "1px solid #d9d9d9",
              borderRadius: "6px",
              fontSize: "12px",
            }}
          />
        ) : (
          <span>
            {editable[record.animal_id]?.dataCollectionDate
              ? new Date(editable[record.animal_id].dataCollectionDate).toLocaleDateString()
              : "-"}
          </span>
        )
      },
    },
    {
      title: "Milk Production Date",
      dataIndex: "milkProductionDate",
      key: "milkProductionDate",
      width: 160,
      render: (_, record) => {
        const editing = isEditing(record)
        return editing ? (
          <input
            type="date"
            value={editable[record.animal_id]?.milkProductionDate || ""}
            onChange={(e) => handleEditChange(record.animal_id, "milkProductionDate", e.target.value)}
            style={{
              width: "100%",
              padding: "4px 8px",
              border: "1px solid #d9d9d9",
              borderRadius: "6px",
              fontSize: "12px",
            }}
          />
        ) : (
          <span>{editable[record.animal_id]?.milkProductionDate || "-"}</span>
        )
      },
    },
    {
      title: "Actions",
      dataIndex: "actions",
      key: "actions",
      width: 120,
      render: (_, record) => {
        const editing = isEditing(record)
        return editing ? (
          <Space>
            <Button icon={<SaveOutlined />} onClick={() => save()} size="small" type="primary" />
            <Button icon={<CloseOutlined />} onClick={cancel} size="small" />
          </Space>
        ) : (
          <Button icon={<EditOutlined />} onClick={() => edit(record)} size="small" disabled={editingKey !== ""}>
            Edit
          </Button>
        )
      },
    },
  ]

  // Chart generation function (keeping the same logic as original)
  const handleGenerateChart = () => {
    console.log('Generate chart clicked, selected animals:', selected.length);
    const selectedAnimals = animals.filter((a) => selected.includes(a.animal_id))
    console.log('Filtered selected animals:', selectedAnimals.length);

    if (selectedAnimals.length === 0) {
      console.log('No animals selected, clearing chart');
      setChartData(null)
      setChartOptions(null)
      return
    }

    try {
      const dataArray = selectedAnimals.map((animal, idx) => {
        const edit = editable[animal.animal_id] || {}
        let breed = edit.breed
        if (typeof breed === "object" && breed !== null && "value" in breed) {
          breed = breed.value
        }
        const animalData = {
          id: idx + 1,
          month_since_pregnancy: Number.parseInt(edit.monthsSincePregnancy || 0),
          month_since_calving: Number.parseInt(edit.monthsSinceCalving || 0),
          avg_lpd_of_month: Number.parseFloat(edit.milkProduction || 0),
          date_of_avg_lpd: edit.milkProductionDate ? new Date(edit.milkProductionDate) : new Date(),
          data_collection_date: edit.dataCollectionDate ? new Date(edit.dataCollectionDate) : new Date(),
          bcs: Number.parseInt(edit.bodyConditionScore || 3),
          breed: breed || "HF",
          calvings: Number.parseInt(edit.calvingNumber || 1),
          age: Number.parseInt(edit.age || 3),
        }
        console.log('Animal data for chart:', animalData);
        return animalData;
      })

      console.log('Data array for chart generation:', dataArray);

      // Chart generation logic
      const allPlotData = dataArray.map((data) => {
        console.log('Generating plot points for animal:', data.id);
        const plotData = getPlotPoints(data)
        console.log('Plot data generated:', plotData);
        return {
          ...plotData,
          cattleId: data.id,
        }
      })

      console.log('All plot data:', allPlotData);

      // Calculate chart bounds
      let minStartYear0 = Math.min(...allPlotData.map(plot => plot.start_year)) || new Date().getFullYear();
      let maxStartYear0 = Math.max(...allPlotData.map(plot => plot.start_year)) || new Date().getFullYear();
      let minStartYearMonths = 0;

      allPlotData.forEach(plot => {
        if (plot.start_year === minStartYear0) {
          minStartYearMonths = Math.min(...plot.x);
        }
      });

      let maxEndYear0 = Math.max(...allPlotData.map(plot => plot.end_year)) || new Date().getFullYear();
      let maxEndYearMonths = 0;

      allPlotData.forEach(plot => {
        if (plot.end_year === maxEndYear0) {
          maxEndYearMonths = Math.max(...plot.x);
        }
      });

      // Generate chart datasets
      const datasets = allPlotData.map((plot, index) => {
        const colors = [
          'rgba(255, 99, 132, 0.8)',
          'rgba(54, 162, 235, 0.8)',
          'rgba(255, 205, 86, 0.8)',
          'rgba(75, 192, 192, 0.8)',
          'rgba(153, 102, 255, 0.8)',
          'rgba(255, 159, 64, 0.8)',
        ];

        const chartData = plot.x.map((x, i) => ({ x, y: plot.y[i] }));
        console.log(`Dataset for Animal ${plot.cattleId}:`, chartData);

        return {
          label: `Animal ${plot.cattleId}`,
          data: chartData,
          borderColor: colors[index % colors.length],
          backgroundColor: colors[index % colors.length].replace('0.8', '0.2'),
          borderWidth: 3,
          fill: false,
          tension: 0.1,
          pointRadius: 4,
          pointHoverRadius: 6,
          pointBackgroundColor: colors[index % colors.length],
          pointBorderColor: '#fff',
          pointBorderWidth: 2,
        };
      });

      console.log('Final datasets for chart:', datasets);

      // Set chart data
      const finalChartData = {
        datasets: datasets,
      };
      console.log('Setting chart data:', finalChartData);
      setChartData(finalChartData);

      // Create annotations for key points
      const annotations = {};
      allPlotData.forEach((plot, animalIndex) => {
        const colors = [
          'rgba(255, 99, 132, 0.8)',
          'rgba(54, 162, 235, 0.8)',
          'rgba(255, 205, 86, 0.8)',
          'rgba(75, 192, 192, 0.8)',
          'rgba(153, 102, 255, 0.8)',
          'rgba(255, 159, 64, 0.8)',
        ];

        // Find key points to annotate
        const maxMilkIndex = plot.y.indexOf(Math.max(...plot.y));
        const dataCollectionIndex = plot.data_collection_month || 0;

        // Peak milk production annotation
        annotations[`peak_animal_${plot.cattleId}`] = {
          type: 'point',
          xValue: plot.x[maxMilkIndex],
          yValue: plot.y[maxMilkIndex],
          backgroundColor: colors[animalIndex % colors.length],
          borderColor: '#fff',
          borderWidth: 2,
          radius: 8,
          label: {
            enabled: true,
            content: `Peak: ${plot.y[maxMilkIndex].toFixed(1)}L`,
            position: 'top',
            backgroundColor: colors[animalIndex % colors.length],
            color: '#fff',
            font: {
              size: 10,
              weight: 'bold'
            },
            padding: 4,
            cornerRadius: 4,
          }
        };

        // Data collection point annotation
        if (dataCollectionIndex < plot.x.length) {
          annotations[`data_collection_animal_${plot.cattleId}`] = {
            type: 'point',
            xValue: plot.x[dataCollectionIndex],
            yValue: plot.y[dataCollectionIndex],
            backgroundColor: '#ff6b6b',
            borderColor: '#fff',
            borderWidth: 2,
            radius: 6,
            label: {
              enabled: true,
              content: `Current: ${plot.y[dataCollectionIndex].toFixed(1)}L`,
              position: 'bottom',
              backgroundColor: '#ff6b6b',
              color: '#fff',
              font: {
                size: 10,
                weight: 'bold'
              },
              padding: 4,
              cornerRadius: 4,
            }
          };
        }

        // Pregnancy start annotation (around month 4-6 typically)
        const pregnancyStartIndex = plot.x.findIndex(x => x >= 4 && x <= 6);
        if (pregnancyStartIndex !== -1) {
          annotations[`pregnancy_animal_${plot.cattleId}`] = {
            type: 'point',
            xValue: plot.x[pregnancyStartIndex],
            yValue: plot.y[pregnancyStartIndex],
            backgroundColor: '#4ecdc4',
            borderColor: '#fff',
            borderWidth: 2,
            radius: 5,
            label: {
              enabled: true,
              content: `Pregnancy: ${plot.y[pregnancyStartIndex].toFixed(1)}L`,
              position: 'left',
              backgroundColor: '#4ecdc4',
              color: '#fff',
              font: {
                size: 9,
                weight: 'bold'
              },
              padding: 3,
              cornerRadius: 4,
            }
          };
        }

        // Dry period annotation (typically around month 22-24)
        const dryPeriodIndex = plot.x.findIndex(x => x >= 22 && x <= 24);
        if (dryPeriodIndex !== -1 && plot.y[dryPeriodIndex] < 5) {
          annotations[`dry_period_animal_${plot.cattleId}`] = {
            type: 'point',
            xValue: plot.x[dryPeriodIndex],
            yValue: plot.y[dryPeriodIndex],
            backgroundColor: '#ffa726',
            borderColor: '#fff',
            borderWidth: 2,
            radius: 5,
            label: {
              enabled: true,
              content: `Dry Period`,
              position: 'top',
              backgroundColor: '#ffa726',
              color: '#fff',
              font: {
                size: 9,
                weight: 'bold'
              },
              padding: 3,
              cornerRadius: 4,
            }
          };
        }

        // Add lactation phase zones as background annotations
        if (animalIndex === 0) { // Only add phase zones once
          annotations['early_lactation_zone'] = {
            type: 'box',
            xMin: 0,
            xMax: 3,
            backgroundColor: 'rgba(76, 175, 80, 0.1)',
            borderColor: 'rgba(76, 175, 80, 0.3)',
            borderWidth: 1,
            label: {
              enabled: true,
              content: 'Early Lactation',
              position: 'center',
              color: 'rgba(76, 175, 80, 0.8)',
              font: {
                size: 11,
                weight: 'bold'
              }
            }
          };

          annotations['peak_lactation_zone'] = {
            type: 'box',
            xMin: 3,
            xMax: 6,
            backgroundColor: 'rgba(255, 193, 7, 0.1)',
            borderColor: 'rgba(255, 193, 7, 0.3)',
            borderWidth: 1,
            label: {
              enabled: true,
              content: 'Peak Lactation',
              position: 'center',
              color: 'rgba(255, 193, 7, 0.8)',
              font: {
                size: 11,
                weight: 'bold'
              }
            }
          };

          annotations['mid_lactation_zone'] = {
            type: 'box',
            xMin: 6,
            xMax: 12,
            backgroundColor: 'rgba(33, 150, 243, 0.1)',
            borderColor: 'rgba(33, 150, 243, 0.3)',
            borderWidth: 1,
            label: {
              enabled: true,
              content: 'Mid Lactation',
              position: 'center',
              color: 'rgba(33, 150, 243, 0.8)',
              font: {
                size: 11,
                weight: 'bold'
              }
            }
          };

          annotations['late_lactation_zone'] = {
            type: 'box',
            xMin: 12,
            xMax: 20,
            backgroundColor: 'rgba(156, 39, 176, 0.1)',
            borderColor: 'rgba(156, 39, 176, 0.3)',
            borderWidth: 1,
            label: {
              enabled: true,
              content: 'Late Lactation',
              position: 'center',
              color: 'rgba(156, 39, 176, 0.8)',
              font: {
                size: 11,
                weight: 'bold'
              }
            }
          };
        }
      });

      // Set chart options
      const finalChartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: true,
            text: 'Milk Production Projection with Key Milestones',
            font: { size: 18, weight: 'bold' },
            color: '#1f2937',
          },
          legend: {
            display: true,
            position: 'top',
            labels: {
              usePointStyle: true,
              padding: 20,
              font: { size: 12 },
            },
          },
          tooltip: {
            mode: 'index',
            intersect: false,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            titleColor: '#fff',
            bodyColor: '#fff',
            borderColor: '#ddd',
            borderWidth: 1,
            callbacks: {
              title: function(context) {
                return `Month ${context[0].parsed.x}`;
              },
              label: function(context) {
                return `${context.dataset.label}: ${context.parsed.y.toFixed(2)} L/day`;
              },
              afterBody: function(context) {
                const month = context[0].parsed.x;
                let phase = '';
                if (month <= 3) phase = 'Early Lactation';
                else if (month <= 6) phase = 'Peak Lactation';
                else if (month <= 12) phase = 'Mid Lactation';
                else if (month <= 20) phase = 'Late Lactation';
                else phase = 'Dry Period';
                return [`Phase: ${phase}`];
              }
            }
          },
          annotation: {
            annotations: annotations
          },
        },
        scales: {
          x: {
            type: 'linear',
            position: 'bottom',
            title: {
              display: true,
              text: 'Months from Start',
              font: { size: 14, weight: 'bold' },
              color: '#374151',
            },
            grid: {
              color: 'rgba(0, 0, 0, 0.1)',
            },
            ticks: {
              color: '#6b7280',
            },
          },
          y: {
            title: {
              display: true,
              text: 'Milk Production (Liters per Day)',
              font: { size: 14, weight: 'bold' },
              color: '#374151',
            },
            min: 0,
            grid: {
              color: 'rgba(0, 0, 0, 0.1)',
            },
            ticks: {
              color: '#6b7280',
            },
          },
        },
        interaction: {
          intersect: false,
          mode: 'index',
        },
        elements: {
          point: {
            radius: 4,
            hoverRadius: 6,
          },
          line: {
            borderWidth: 3,
          },
        },
      };
      console.log('Setting chart options:', finalChartOptions);
      setChartOptions(finalChartOptions);
    } catch (err) {
      console.error('Error generating chart:', err);
      setChartData(null)
      setChartOptions(null)
      alert(`Error generating chart: ${err.message || "Unknown error"}`)
    }
  }

  return (
    <div
      style={{
        background: "#f0f2f5",
        minHeight: "100vh",
        padding: "24px",
        fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      }}
    >
      <div style={{ maxWidth: 1400, margin: "0 auto" }}>
        {/* Header */}
        <div style={{ marginBottom: 24 }}>
          <h1
            style={{
              fontSize: 32,
              fontWeight: 700,
              color: "#1f2937",
              margin: 0,
              marginBottom: 8,
              letterSpacing: "-0.5px",
            }}
          >
            Animal Management Dashboard
          </h1>
          <p
            style={{
              fontSize: 16,
              color: "#6b7280",
              margin: 0,
            }}
          >
            Manage animals and generate milk production projections
          </p>
        </div>

        {/* Stats Cards */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="Total Animals"
                value={animals.length}
                prefix={<TeamOutlined />}
                valueStyle={{ color: "#1890ff" }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="Selected"
                value={selected.length}
                prefix={<UserOutlined />}
                valueStyle={{ color: "#52c41a" }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="Currently Editing"
                value={editingKey ? 1 : 0}
                prefix={<EditOutlined />}
                valueStyle={{ color: "#fa8c16" }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Button
                type="primary"
                size="large"
                icon={<BarChartOutlined />}
                onClick={handleGenerateChart}
                disabled={selected.length === 0}
                style={{ width: "100%", height: 60 }}
              >
                Generate Chart
              </Button>
            </Card>
          </Col>
        </Row>

        {/* Farmer Information */}
        {farmer && (
          <Card
            title={
              <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
                <UserOutlined style={{ color: "#1890ff" }} />
                Farmer Information
              </div>
            }
            style={{ marginBottom: 24 }}
          >
            <Table
              columns={farmerTableColumns}
              dataSource={getFarmerTableData(farmer)}
              pagination={false}
              size="small"
              showHeader={false}
            />
          </Card>
        )}

        {/* Animals Table */}
        <Card
          title={
            <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
              <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
                <TeamOutlined style={{ color: "#1890ff" }} />
                Animals ({animals.length})
              </div>
              {selected.length > 0 && <Tag color="blue">{selected.length} selected</Tag>}
            </div>
          }
          style={{ marginBottom: 24 }}
        >
          {loading ? (
            <div style={{ display: "flex", justifyContent: "center", alignItems: "center", minHeight: 200 }}>
              <Spin size="large" />
            </div>
          ) : error ? (
            <Alert type="error" message={error} showIcon />
          ) : animals.length === 0 ? (
            <div style={{ textAlign: "center", color: "#888", fontSize: 16, padding: 40 }}>
              No animals found for this farmer.
            </div>
          ) : (
            <Table
              columns={getAnimalColumns()}
              dataSource={animals}
              rowKey="animal_id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} animals`,
              }}
              scroll={{ x: 1600 }}
              size="small"
              rowSelection={{
                selectedRowKeys: selected,
                onChange: (selectedRowKeys) => {
                  setSelected(selectedRowKeys)
                },
                getCheckboxProps: (record) => ({
                  name: record.animal_id,
                }),
              }}
            />
          )}
        </Card>

        {/* Chart Section */}
        {chartData && chartOptions && (
          <Card
            title={
              <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
                <BarChartOutlined style={{ color: "#1890ff" }} />
                Milk Production Forecast with Key Milestones
              </div>
            }
          >
            {/* Annotation Legend */}
            <div style={{
              marginBottom: 16,
              padding: 12,
              backgroundColor: '#f8fafc',
              borderRadius: 8,
              border: '1px solid #e5e7eb'
            }}>
              <div style={{
                fontSize: 14,
                fontWeight: 600,
                marginBottom: 8,
                color: '#374151'
              }}>
                Chart Annotations:
              </div>
              <div style={{
                display: 'flex',
                flexWrap: 'wrap',
                gap: 16,
                fontSize: 12
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
                  <div style={{
                    width: 12,
                    height: 12,
                    borderRadius: '50%',
                    backgroundColor: 'rgba(255, 99, 132, 0.8)',
                    border: '2px solid #fff'
                  }}></div>
                  <span>Peak Production</span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
                  <div style={{
                    width: 12,
                    height: 12,
                    borderRadius: '50%',
                    backgroundColor: '#ff6b6b',
                    border: '2px solid #fff'
                  }}></div>
                  <span>Current Data Point</span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
                  <div style={{
                    width: 12,
                    height: 12,
                    borderRadius: '50%',
                    backgroundColor: '#4ecdc4',
                    border: '2px solid #fff'
                  }}></div>
                  <span>Pregnancy Start</span>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
                  <div style={{
                    width: 12,
                    height: 12,
                    borderRadius: '50%',
                    backgroundColor: '#ffa726',
                    border: '2px solid #fff'
                  }}></div>
                  <span>Dry Period</span>
                </div>
              </div>
            </div>

            <div style={{ height: 500 }}>
              <Line data={chartData} options={chartOptions} />
            </div>
          </Card>
        )}
      </div>
    </div>
  )
}

export default FarmerAnimals

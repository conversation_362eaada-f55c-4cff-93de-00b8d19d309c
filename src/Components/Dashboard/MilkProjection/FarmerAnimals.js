import { useState, useEffect } from 'react';
import { useParams, useLocation } from 'react-router-dom';
import { Spin, Alert, Table, Select, InputNumber, Button, Space, Image, Tag } from 'antd';
import { UserOutlined, PhoneOutlined, HomeOutlined, EnvironmentOutlined, IdcardOutlined, EditOutlined, SaveOutlined, CloseOutlined } from '@ant-design/icons';
import { useSelector } from 'react-redux';
import { fetchAnimalsForFarmer } from './api';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import annotationPlugin from 'chartjs-plugin-annotation';
import { getPlotPoints } from './plot';

const { Option } = Select;

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  annotationPlugin
);

const placeholderImg = 'https://cdn-icons-png.flaticon.com/512/616/616408.png';

// Helper to get a displayable value from a field
function getDisplayValue(value) {
  if (value === null || value === undefined || value === '') return '';
  if (typeof value === 'object') {
    // Handle objects with language keys
    if (typeof value.en === 'string') return value.en;
    if (typeof value.mr === 'string') return value.mr;
    if (typeof value.value === 'string') return value.value;
    if (typeof value.label === 'string') return value.label;
    // If it's an array, join with commas
    if (Array.isArray(value)) return value.map(v => String(v)).join(', ');
    // For other objects, try to extract a meaningful string
    const firstString = Object.values(value).find(v => typeof v === 'string');
    return firstString || '';
  }
  return String(value);
}



const getS3ImageUrl = async (animal_document_id, token) => {
  try {
    const response = await fetch(`/api/document?document_id=${animal_document_id}`, {
      headers: { 'token': token }
    });
    if (!response.ok) return null;
    const data = await response.json();
    // Try to find the image URL in the response
    if (data?.data?.url) return data.data.url;
    if (data?.data?.doc_key) return data.data.doc_key;
    return null;
  } catch {
    return null;
  }
};

const CATTLE_TYPES = [
  { value: 'cow', label: 'Cow' },
  { value: 'buffalo', label: 'Buffalo' },
];
const BREEDS = [
  { value: 'HF', label: 'HF' },
  { value: 'Jersey', label: 'Jersey' },
  { value: 'Other', label: 'Other' },
];
const CALVINGS = [1,2,3,4,5,6,7];
const BCS = [
  { value: '1.0', label: '1.0 - Emaciated' },
  { value: '1.5', label: '1.5 - Very Thin' },
  { value: '2.0', label: '2.0 - Thin' },
  { value: '2.5', label: '2.5 - Slightly Thin' },
  { value: '3.0', label: '3.0 - Good' },
  { value: '3.5', label: '3.5 - Above Average' },
  { value: '4.0', label: '4.0 - Fat' },
  { value: '4.5', label: '4.5 - Very Fat' },
  { value: '5.0', label: '5.0 - Obese' },
];

function getInitialEditable(animal) {
  let breed = getDisplayValue(animal.animal_breed_1);
  if (breed !== 'HF' && breed !== 'Jersey') {
    breed = 'HF';
  }
  return {
    cattleType: getDisplayValue(animal.animal_type) || 'cow',
    breed,
    calvingNumber: getDisplayValue(animal.calving_number) || 1,
    age: getDisplayValue(animal.animal_age_1) || '',
    dataCollectionDate: '',
    monthsSinceCalving: getDisplayValue(animal.months_since_calving) || '',
    monthsSincePregnancy: getDisplayValue(animal.months_since_pregnancy) || '',
    bodyConditionScore: getDisplayValue(animal.body_condition_score) || '3.0',
    milkProduction: getDisplayValue(animal.milk_production) || '',
    milkProductionDate: '',
  };
}

const month_name = {
  1: 'January', 2: 'February', 3: 'March', 4: 'April', 5: 'May', 6: 'June',
  7: 'July', 8: 'August', 9: 'September', 10: 'October', 11: 'November', 12: 'December'
};

const FarmerAnimals = () => {
  const { farmerId } = useParams();
  const location = useLocation();
  const farmer = location.state?.farmer;
  const userToken = useSelector((state) => state.user.userToken);
  const [animals, setAnimals] = useState([]);
  const [selected, setSelected] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [animalImages, setAnimalImages] = useState({}); // animal_id -> image url
  const [editable, setEditable] = useState({}); // animal_id -> editable fields
  const [editingKey, setEditingKey] = useState(''); // Currently editing row
  const [chartData, setChartData] = useState(null);
  const [chartOptions, setChartOptions] = useState(null);

  useEffect(() => {
    const fetchAnimals = async () => {
      if (!userToken || !farmerId) return;
      setLoading(true);
      setError(null);
      try {
        const animalsData = await fetchAnimalsForFarmer(userToken, farmerId);
        setAnimals(animalsData);
      } catch (err) {
        setError('Failed to fetch animals.');
        setAnimals([]);
      } finally {
        setLoading(false);
      }
    };
    fetchAnimals();
  }, [userToken, farmerId]);

  // Fetch S3 images for animals with animal_document_id
  useEffect(() => {
    const fetchImages = async () => {
      if (!userToken) return;
      const promises = animals.map(async animal => {
        if (animal.animal_document_id) {
          const url = await getS3ImageUrl(animal.animal_document_id, userToken.accessToken);
          return { animal_id: animal.animal_id, url };
        }
        return null;
      });
      const results = await Promise.all(promises);
      const imageMap = {};
      results.forEach(res => {
        if (res && res.url) imageMap[res.animal_id] = res.url;
      });
      setAnimalImages(imageMap);
    };
    if (animals.length > 0) fetchImages();
  }, [animals, userToken]);

  useEffect(() => {
    if (animals.length > 0) {
      const initial = {};
      animals.forEach(animal => {
        initial[animal.animal_id] = getInitialEditable(animal);
      });
      setEditable(initial);
    }
  }, [animals]);



  const handleEditChange = (animalId, field, value) => {
    setEditable(prev => ({
      ...prev,
      [animalId]: {
        ...prev[animalId],
        [field]: value,
      },
    }));
  };

  // Collapse/Expand all handler


  // Editing functions
  const isEditing = (record) => record.animal_id === editingKey;

  const edit = (record) => {
    setEditingKey(record.animal_id);
  };

  const cancel = () => {
    setEditingKey('');
  };

  const save = async () => {
    try {
      setEditingKey('');
      // Here you could add API call to save the data
    } catch (errInfo) {
      console.log('Validate Failed:', errInfo);
    }
  };

  // Farmer table configuration
  const farmerTableColumns = [
    {
      title: 'Field',
      dataIndex: 'field',
      key: 'field',
      width: 150,
      render: (text, record) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          {record.icon}
          <span style={{ fontWeight: 600, color: '#1890ff' }}>{text}</span>
        </div>
      ),
    },
    {
      title: 'Value',
      dataIndex: 'value',
      key: 'value',
      render: (text) => (
        <span style={{ fontWeight: 500, color: '#23272f' }}>{text}</span>
      ),
    },
  ];

  const getFarmerTableData = (farmer) => [
    {
      key: 'name',
      field: 'Name',
      value: farmer.customer_name || '-',
      icon: <UserOutlined style={{ color: '#1890ff' }} />
    },
    {
      key: 'mobile',
      field: 'Mobile',
      value: farmer.mobile_number || '-',
      icon: <PhoneOutlined style={{ color: '#52c41a' }} />
    },
    {
      key: 'village',
      field: 'Village',
      value: farmer.village || '-',
      icon: <HomeOutlined style={{ color: '#fa8c16' }} />
    },
    {
      key: 'taluk',
      field: 'Taluk',
      value: farmer.taluk || '-',
      icon: <EnvironmentOutlined style={{ color: '#722ed1' }} />
    },
    {
      key: 'farmerId',
      field: 'Farmer ID',
      value: farmer.customer_visual_id || '-',
      icon: <IdcardOutlined style={{ color: '#eb2f96' }} />
    },
  ];

  // Animal table columns configuration
  const getAnimalColumns = () => [
    {
      title: 'Image',
      dataIndex: 'image',
      key: 'image',
      width: 80,
      render: (_, record) => {
        let imgUrl = animalImages[record.animal_id];
        if (!imgUrl && record.document_information?.url) {
          imgUrl = `https://ahs3.krushal.in/${record.document_information.url}`;
        }
        if (!imgUrl) imgUrl = placeholderImg;

        return (
          <Image
            width={60}
            height={60}
            src={imgUrl}
            style={{ borderRadius: '8px', objectFit: 'cover' }}
            fallback={placeholderImg}
          />
        );
      },
    },
    {
      title: 'Animal Info',
      dataIndex: 'info',
      key: 'info',
      width: 200,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 600, fontSize: '14px', marginBottom: 4 }}>
            {getDisplayValue(record.animal_name) || getDisplayValue(record.animal_visual_id) || record.animal_id}
          </div>
          <div style={{ color: '#666', fontSize: '12px' }}>
            {getDisplayValue(record.animal_type) || '-'}
          </div>
          <div style={{ color: '#666', fontSize: '12px' }}>
            Age: {getDisplayValue(record.animal_age_1) || '-'}
          </div>
        </div>
      ),
    },
    {
      title: 'Breed',
      dataIndex: 'breed',
      key: 'breed',
      width: 120,
      render: (_, record) => {
        const editing = isEditing(record);
        return editing ? (
          <Select
            value={editable[record.animal_id]?.breed || 'HF'}
            onChange={(value) => handleEditChange(record.animal_id, 'breed', value)}
            style={{ width: '100%' }}
            size="small"
          >
            {BREEDS.map(opt => (
              <Option key={opt.value} value={opt.value}>{opt.label}</Option>
            ))}
          </Select>
        ) : (
          <Tag color="blue">{editable[record.animal_id]?.breed || 'HF'}</Tag>
        );
      },
    },
    {
      title: 'Cattle Type',
      dataIndex: 'cattleType',
      key: 'cattleType',
      width: 120,
      render: (_, record) => {
        const editing = isEditing(record);
        return editing ? (
          <Select
            value={editable[record.animal_id]?.cattleType || 'cow'}
            onChange={(value) => handleEditChange(record.animal_id, 'cattleType', value)}
            style={{ width: '100%' }}
            size="small"
          >
            {CATTLE_TYPES.map(opt => (
              <Option key={opt.value} value={opt.value}>{opt.label}</Option>
            ))}
          </Select>
        ) : (
          <Tag color="green">{editable[record.animal_id]?.cattleType || 'cow'}</Tag>
        );
      },
    },
    {
      title: 'Age',
      dataIndex: 'age',
      key: 'age',
      width: 80,
      render: (_, record) => {
        const editing = isEditing(record);
        return editing ? (
          <InputNumber
            min={2}
            max={15}
            value={editable[record.animal_id]?.age || ''}
            onChange={(value) => handleEditChange(record.animal_id, 'age', value)}
            style={{ width: '100%' }}
            size="small"
          />
        ) : (
          <span>{editable[record.animal_id]?.age || '-'}</span>
        );
      },
    },
    {
      title: 'Calving #',
      dataIndex: 'calvingNumber',
      key: 'calvingNumber',
      width: 100,
      render: (_, record) => {
        const editing = isEditing(record);
        return editing ? (
          <Select
            value={editable[record.animal_id]?.calvingNumber || 1}
            onChange={(value) => handleEditChange(record.animal_id, 'calvingNumber', value)}
            style={{ width: '100%' }}
            size="small"
          >
            {CALVINGS.map(num => (
              <Option key={num} value={num}>{num}</Option>
            ))}
          </Select>
        ) : (
          <span>{editable[record.animal_id]?.calvingNumber || 1}</span>
        );
      },
    },
    {
      title: 'BCS',
      dataIndex: 'bodyConditionScore',
      key: 'bodyConditionScore',
      width: 120,
      render: (_, record) => {
        const editing = isEditing(record);
        const bcsValue = editable[record.animal_id]?.bodyConditionScore || '3.0';
        const bcsLabel = BCS.find(b => b.value === bcsValue)?.label || bcsValue;

        return editing ? (
          <Select
            value={bcsValue}
            onChange={(value) => handleEditChange(record.animal_id, 'bodyConditionScore', value)}
            style={{ width: '100%' }}
            size="small"
          >
            {BCS.map(opt => (
              <Option key={opt.value} value={opt.value}>{opt.label}</Option>
            ))}
          </Select>
        ) : (
          <Tag color={parseFloat(bcsValue) >= 3.0 ? 'green' : 'orange'}>
            {bcsLabel}
          </Tag>
        );
      },
    },
    {
      title: 'Milk Production (L)',
      dataIndex: 'milkProduction',
      key: 'milkProduction',
      width: 150,
      render: (_, record) => {
        const editing = isEditing(record);
        return editing ? (
          <InputNumber
            min={0}
            step={0.1}
            value={editable[record.animal_id]?.milkProduction || ''}
            onChange={(value) => handleEditChange(record.animal_id, 'milkProduction', value)}
            style={{ width: '100%' }}
            size="small"
          />
        ) : (
          <span>{(editable[record.animal_id]?.milkProduction || '-')} L</span>
        );
      },
    },
    {
      title: 'Actions',
      dataIndex: 'actions',
      key: 'actions',
      width: 120,
      render: (_, record) => {
        const editing = isEditing(record);
        return editing ? (
          <Space>
            <Button
              icon={<SaveOutlined />}
              onClick={() => save()}
              size="small"
              type="primary"
            />
            <Button
              icon={<CloseOutlined />}
              onClick={cancel}
              size="small"
            />
          </Space>
        ) : (
          <Button
            icon={<EditOutlined />}
            onClick={() => edit(record)}
            size="small"
            disabled={editingKey !== ''}
          >
            Edit
          </Button>
        );
      },
    },
  ];



  const selectedAnimals = animals.filter(a => selected.includes(a.animal_id));

  // Add a function to generate chart data from selected animals
  const handleGenerateChart = () => {
    const selectedAnimals = animals.filter(a => selected.includes(a.animal_id));
    if (selectedAnimals.length === 0) {
      setChartData(null);
      setChartOptions(null);
      return;
    }
    try {
      const dataArray = selectedAnimals.map((animal, idx) => {
        const edit = editable[animal.animal_id] || {};
        let breed = edit.breed;
        if (typeof breed === 'object' && breed !== null && 'value' in breed) {
          breed = breed.value;
        }
        return {
          id: idx + 1,
          month_since_pregnancy: parseInt(edit.monthsSincePregnancy || 0),
          month_since_calving: parseInt(edit.monthsSinceCalving || 0),
          avg_lpd_of_month: parseFloat(edit.milkProduction || 0),
          date_of_avg_lpd: edit.milkProductionDate ? new Date(edit.milkProductionDate) : new Date(),
          data_collection_date: edit.dataCollectionDate ? new Date(edit.dataCollectionDate) : new Date(),
          bcs: parseInt(edit.bodyConditionScore || 3),
          breed: breed || 'HF',
          calvings: parseInt(edit.calvingNumber || 1),
          age: parseInt(edit.age || 3)
        };
      });

      // Get plot data for each animal
      const allPlotData = dataArray.map((data) => {
        const plotData = getPlotPoints(data);
        return {
          ...plotData,
          cattleId: data.id
        };
      });
      // Find min/max years and months for x-axis
      let minStartYear0 = Math.min(...allPlotData.map(plot => plot.start_year)) || new Date().getFullYear();
      let maxStartYear0 = Math.max(...allPlotData.map(plot => plot.start_year)) || new Date().getFullYear();
      let minStartYearMonths = 0;
      let maxStartYearMonths = 0;
      allPlotData.forEach(plot => {
        if (plot.start_year === minStartYear0) {
          minStartYearMonths = Math.min(...plot.x);
        }
        if (plot.start_year === maxStartYear0) {
          maxStartYearMonths = Math.max(...plot.x);
        }
      });
      const month_labels = Array.from({length: 33}, (_, i) => {
        const monthNum = minStartYearMonths + i;
        const monthIdx = (monthNum % 12) === 0 ? 12 : monthNum % 12;
        return {
          key: `${monthIdx % 12 === 0 ? minStartYear0++ : minStartYear0}-${monthIdx}-${month_name[monthIdx]}`,
          name: month_name[monthIdx],
          month: monthNum
        };
      });
      
      const lineColors = ['#3b82f6','#ef4444','#22c55e','#f97316','#8b5cf6','#06b6d4','#ec4899','#f59e0b'];

      // Year backgrounds
      const monthsPerYear = 12;
      let minStartYear = Math.min(...allPlotData.map(plot => plot.start_year)) || new Date().getFullYear();
      const yearBackgrounds = {};
      const totalYears = Math.ceil(month_labels.length / monthsPerYear);

      for (let i = 0; i < totalYears; i++) {
        const year = minStartYear + i;
        yearBackgrounds[`year${year}`] = {
          type: 'box',
          xMin: i * monthsPerYear,
          xMax: Math.min((i + 1) * monthsPerYear - 1, month_labels.length - 1),
          yMin: 'min',
          yMax: 'max',
          backgroundColor: 'rgba(0,0,0,0)',
          borderWidth: 0,
          drawTime: 'beforeDatasetsDraw',
          label: {
            display: true,
            content: year.toString(),
            position: {x: 'start', y: 'start'},
            padding: {top: 4, left: 8},
            color: 'rgba(107, 114, 128, 0.8)',
            font: { size: 14, weight: '500', family: 'system-ui' }
          }
        };
      }
      const annotations = {};
      month_labels.forEach((label, index) => {
        allPlotData.forEach((plotData, cattleIndex) => {
          const value = plotData.map[label.key];
          if (typeof value === 'object' && value.message && value.message !== 'Production Data') {
            annotations[`line${index}-${cattleIndex}`] = {
              type: 'line',
              xMin: index,
              xMax: index,
              borderColor: 'rgba(0,0,0,0)',
              borderWidth: 1,
              borderDash: [5, 5],
              borderDashOffset: cattleIndex * 2,
            };
          }
        });
        const month = label.month;
        if (month % 12 === 1 && index > 0) {
          annotations[`yearTransition${index}`] = {
            type: 'line',
            xMin: index,
            xMax: index,
            borderColor: 'rgba(75, 85, 99, 1)',
            borderWidth: 2,
          };
        }
      });
      // Chart.js data and options
      setChartData({
        labels: month_labels.map(label => label.name),
        datasets: allPlotData.map((plotData, index) => ({
          label: `Cattle #${plotData.cattleId}`,
          data: month_labels.map(label => {
            const value = plotData.map[label.key];
            return typeof value === 'object' ? value.lpd : value;
          }),
          borderColor: lineColors[index % lineColors.length],
          backgroundColor: 'transparent',
          borderWidth: 1,
          fill: false,
          tension: 0.4,
          pointRadius: month_labels.map(label => {
            const value = plotData.map[label.key];
            return value ? value.pointRadius : undefined;
          }),
          pointHoverRadius: month_labels.map(() => 8),
          pointStyle: month_labels.map(label => {
            const value = plotData.map[label.key];
            return value ? value.pointStyle : undefined;
          }),
          pointBackgroundColor: month_labels.map(label => {
            const value = plotData.map[label.key];
            return value ? value.color : undefined;
          }),
          pointBorderColor: 'white',
          pointBorderWidth: 1,
        }))
      });
      setChartOptions({
        responsive: true,
        maintainAspectRatio: false,
        aspectRatio: 1,
        plugins: {
          annotation: {
            common: { drawTime: 'beforeDraw' },
            annotations: { ...yearBackgrounds, ...annotations }
          },
          title: {
            display: true,
            text: `Milk Production Forecast ${new Date().getFullYear()}`,
            font: { size: 18, weight: '500', family: 'system-ui' },
            padding: 20
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                const lpd = context.parsed.y;
                if (lpd === null || lpd === undefined) return null;
                const datasetLabel = context.dataset.label;
                const plotData = allPlotData[context.datasetIndex];
                const label = month_labels[context.dataIndex];
                const value = plotData.map[label.key];
                const message = typeof value === 'object' ? value.message : 'Production Data';
                return [`${datasetLabel}: ${lpd.toFixed(2)} LPD`, `Status: ${message}`];
              }
            }
          }
        },
        scales: {
          x: {
            title: {
              display: true,
              text: 'Month',
              font: { size: 14, weight: '500', family: 'system-ui' },
              padding: {top: 15}
            },
            grid: {
              color: 'rgba(226, 232, 240, 0.6)',
              lineWidth: 1,
              drawBorder: true
            },
            ticks: {
              padding: 10,
              font: { size: 12, family: 'system-ui' }
            },
            offset: true,
          },
          y: {
            title: {
              display: true,
              text: 'Liters Per Day (LPD)',
              font: { size: 14, weight: '500', family: 'system-ui' },
              padding: {right: 15}
            },
            grid: {
              color: 'rgba(226, 232, 240, 0.6)',
              lineWidth: 1,
              drawBorder: false,
              drawTicks: false
            },
            ticks: {
              padding: 10,
              font: { size: 12, family: 'system-ui' }
            },
            beginAtZero: true,
            suggestedMax: Math.ceil(Math.max(
              ...allPlotData.flatMap(plot => 
                Object.values(plot.map)
                  .map(v => typeof v === 'object' ? v.lpd : v)
                  .filter(v => typeof v === 'number')
              )
            ) * 1.1)
          }
        },
        layout: {
          padding: { top: 20, right: 20, bottom: 20, left: 20 }
        }
      });
    } catch (err) {
      setChartData(null);
      setChartOptions(null);
      alert(err.message || 'Error generating chart');
    }
  };

  return (
    <div style={{ width: '100%', minHeight: '100vh', background: '#f8fafc', padding: '20px', boxSizing: 'border-box', fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif' }}>
      <style>{`
        .farmer-table-row-even td {
          background-color: #fafafa !important;
        }
        .farmer-table-row-odd td {
          background-color: #ffffff !important;
        }
        .farmer-table-row-even:hover td,
        .farmer-table-row-odd:hover td {
          background-color: #e6f7ff !important;
        }

        .ant-table-thead > tr > th {
          background: #f8fafc !important;
          border-bottom: 2px solid #e5e7eb !important;
          font-weight: 600 !important;
        }

        .ant-table-tbody > tr:hover > td {
          background: #f0f9ff !important;
        }

        .ant-table-wrapper {
          border-radius: 8px !important;
          overflow: hidden !important;
        }

        @media (max-width: 768px) {
          .ant-table-tbody > tr > td {
            padding: 8px 12px !important;
          }
          .ant-table-thead > tr > th {
            padding: 8px 12px !important;
          }
        }
      `}</style>
      <div style={{ maxWidth: 1400, margin: '0 auto' }}>
        {farmer && (
          <div style={{ marginBottom: 24 }}>
            <div style={{
              background: '#fff',
              borderRadius: 12,
              boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
              overflow: 'hidden',
              maxWidth: 600,
            }}>
              <div style={{
                background: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
                padding: '16px 24px',
                color: '#fff',
                fontSize: '18px',
                fontWeight: 600,
                display: 'flex',
                alignItems: 'center',
                gap: 8
              }}>
                <UserOutlined />
                Farmer Information
              </div>
              <Table
                columns={farmerTableColumns}
                dataSource={getFarmerTableData(farmer)}
                pagination={false}
                size="middle"
                showHeader={false}
                style={{
                  background: '#fff',
                }}
                rowClassName={(_, index) =>
                  index % 2 === 0 ? 'farmer-table-row-even' : 'farmer-table-row-odd'
                }
              />
            </div>
          </div>
        )}

        {/* Animals Section */}
        <div style={{
          background: '#fff',
          borderRadius: 12,
          padding: 20,
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
          border: '1px solid #e5e7eb'
        }}>
          <div style={{
            marginBottom: 20,
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            paddingBottom: 16,
            borderBottom: '1px solid #e5e7eb',
            flexWrap: 'wrap',
            gap: 16
          }}>
            <div>
              <h2 style={{
                margin: 0,
                color: '#1f2937',
                fontSize: 20,
                fontWeight: 600,
                marginBottom: 4
              }}>
                Animals ({animals.length})
              </h2>
              <p style={{
                margin: 0,
                color: '#6b7280',
                fontSize: 14
              }}>
                Manage and edit animal information for milk projection
              </p>
            </div>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: 16,
              flexWrap: 'wrap'
            }}>
              {selected.length > 0 && (
                <div style={{
                  background: '#dbeafe',
                  color: '#1e40af',
                  padding: '6px 12px',
                  borderRadius: 6,
                  fontSize: 14,
                  fontWeight: 500
                }}>
                  {selected.length} selected
                </div>
              )}
              <Button
                type="primary"
                onClick={handleGenerateChart}
                disabled={selected.length === 0}
                style={{ borderRadius: 6 }}
              >
                Generate Chart ({selected.length})
              </Button>
            </div>
          </div>
        {loading ? (
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 160 }}>
            <Spin size="large" />
          </div>
        ) : error ? (
          <Alert type="error" message={error} showIcon style={{ marginBottom: 18 }} />
        ) : animals.length === 0 ? (
          <div style={{ color: '#888', fontSize: 16, marginTop: 32 }}>No animals found for this farmer.</div>
        ) : (
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'flex-start',
              gap: 32,
              width: '100%',
              boxSizing: 'border-box',
              minHeight: 600,
              flexWrap: 'wrap',
            }}
          >
            {/* Left: Animal Table */}
            <div style={{
              flex: 1,
              minWidth: 600,
              padding: 18,
              border: '1px solid #e3e6ea',
              borderRadius: 12,
              background: '#fff',
              boxShadow: '0 2px 8px rgba(60,72,88,0.04)',
              maxHeight: '80vh',
              overflow: 'hidden',
              margin: 0,
              display: 'flex',
              flexDirection: 'column'
            }}>
              <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <h3 style={{ margin: 0, color: '#1890ff', fontSize: 18, fontWeight: 600 }}>
                  Animals ({animals.length})
                </h3>
                <div style={{ fontSize: 14, color: '#666' }}>
                  {selected.length > 0 && `${selected.length} selected`}
                </div>
              </div>

              <Table
                columns={getAnimalColumns()}
                dataSource={animals}
                rowKey="animal_id"
                pagination={{
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} animals`,
                }}
                scroll={{ x: 1200, y: 'calc(80vh - 200px)' }}
                size="small"
                rowSelection={{
                  selectedRowKeys: selected,
                  onChange: (selectedRowKeys) => {
                    setSelected(selectedRowKeys);
                  },
                  getCheckboxProps: (record) => ({
                    name: record.animal_id,
                  }),
                }}
                style={{ flex: 1 }}
              />
          </div>

          {/* Chart Section */}
          {chartData && chartOptions && (
            <div style={{
              marginTop: 24,
              background: '#fff',
              borderRadius: 12,
              padding: 24,
              boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
              border: '1px solid #e5e7eb'
            }}>
              <h2 style={{
                margin: 0,
                marginBottom: 20,
                color: '#1f2937',
                fontSize: 20,
                fontWeight: 600
              }}>
                Milk Production Chart
              </h2>
              <div style={{ width: '100%', height: 500, overflow: 'auto' }}>
                <Line data={chartData} options={chartOptions} height={500} />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FarmerAnimals; 
"use client"

import { useState, useEffect } from "react"
import { usePara<PERSON>, useLocation } from "react-router-dom"
import { Table, Alert, Button, Select, InputNumber, DatePicker, Space, Tag, Avatar } from "antd"
import { EyeOutlined, Bar<PERSON>hartOutlined, ExpandAltOutlined, CompressOutlined, UserOutlined } from "@ant-design/icons"
import { useSelector } from "react-redux"
import { fetchAnimalsForFarmer } from "./api"
import { Line } from "react-chartjs-2"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
} from "chart.js"
import annotationPlugin from "chartjs-plugin-annotation"
import { getPlotPoints } from "./plot"
import dayjs from "dayjs"

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, ChartTooltip, Legend, annotationPlugin)

const { Option } = Select

const placeholderImg = "https://cdn-icons-png.flaticon.com/512/616/616408.png"

const CATTLE_TYPES = [
  { value: "cow", label: "Cow" },
  { value: "buffalo", label: "Buffalo" },
]

const BREEDS = [
  { value: "HF", label: "HF" },
  { value: "Jersey", label: "Jersey" },
  { value: "Other", label: "Other" },
]

const CALVINGS = [1, 2, 3, 4, 5, 6, 7]

const BCS = [
  { value: "1.0", label: "1.0 - Emaciated" },
  { value: "1.5", label: "1.5 - Very Thin" },
  { value: "2.0", label: "2.0 - Thin" },
  { value: "2.5", label: "2.5 - Slightly Thin" },
  { value: "3.0", label: "3.0 - Good" },
  { value: "3.5", label: "3.5 - Above Average" },
  { value: "4.0", label: "4.0 - Fat" },
  { value: "4.5", label: "4.5 - Very Fat" },
  { value: "5.0", label: "5.0 - Obese" },
]

const month_name = {
  1: "January",
  2: "February",
  3: "March",
  4: "April",
  5: "May",
  6: "June",
  7: "July",
  8: "August",
  9: "September",
  10: "October",
  11: "November",
  12: "December",
}

// Helper functions
function getDisplayValue(value) {
  if (value === null || value === undefined || value === "") return ""
  if (typeof value === "object") {
    if (typeof value.en === "string") return value.en
    const firstString = Object.values(value).find((v) => typeof v === "string")
    return firstString || ""
  }
  return value
}

const getS3ImageUrl = async (animal_document_id, token) => {
  try {
    const response = await fetch(`/api/document?document_id=${animal_document_id}`, {
      headers: { token: token },
    })
    if (!response.ok) return null
    const data = await response.json()
    if (data?.data?.url) return data.data.url
    if (data?.data?.doc_key) return data.data.doc_key
    return null
  } catch {
    return null
  }
}

function getInitialEditable(animal) {
  let breed = animal.animal_breed_1
  if (breed !== "HF" && breed !== "Jersey") {
    breed = "HF"
  }
  return {
    cattleType: animal.animal_type || "cow",
    breed,
    calvingNumber: animal.calving_number || 1,
    age: animal.animal_age_1 || "",
    dataCollectionDate: "",
    monthsSinceCalving: animal.months_since_calving || "",
    monthsSincePregnancy: animal.months_since_pregnancy || "",
    bodyConditionScore: animal.body_condition_score || "3.0",
    milkProduction: animal.milk_production || "",
    milkProductionDate: "",
  }
}

const FarmerAnimals = () => {
  const { farmerId } = useParams()
  const location = useLocation()
  const farmer = location.state?.farmer
  const userToken = useSelector((state) => state.user.userToken)

  const [animals, setAnimals] = useState([])
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [animalImages, setAnimalImages] = useState({})
  const [editable, setEditable] = useState({})
  const [expandedRowKeys, setExpandedRowKeys] = useState([])
  const [chartData, setChartData] = useState(null)
  const [chartOptions, setChartOptions] = useState(null)

  // Fetch animals
  useEffect(() => {
    const fetchAnimals = async () => {
      if (!userToken || !farmerId) return
      setLoading(true)
      setError(null)
      try {
        const animalsData = await fetchAnimalsForFarmer(userToken, farmerId)
        setAnimals(animalsData)
      } catch (err) {
        setError("Failed to fetch animals.")
        setAnimals([])
      } finally {
        setLoading(false)
      }
    }
    fetchAnimals()
  }, [userToken, farmerId])

  // Fetch images
  useEffect(() => {
    const fetchImages = async () => {
      if (!userToken) return
      const promises = animals.map(async (animal) => {
        if (animal.animal_document_id) {
          const url = await getS3ImageUrl(animal.animal_document_id, userToken.accessToken)
          return { animal_id: animal.animal_id, url }
        }
        return null
      })
      const results = await Promise.all(promises)
      const imageMap = {}
      results.forEach((res) => {
        if (res && res.url) imageMap[res.animal_id] = res.url
      })
      setAnimalImages(imageMap)
    }
    if (animals.length > 0) fetchImages()
  }, [animals, userToken])

  // Initialize editable data
  useEffect(() => {
    if (animals.length > 0) {
      const initial = {}
      animals.forEach((animal) => {
        initial[animal.animal_id] = getInitialEditable(animal)
      })
      setEditable(initial)
    }
  }, [animals])

  const handleEditChange = (animalId, field, value) => {
    setEditable((prev) => ({
      ...prev,
      [animalId]: {
        ...prev[animalId],
        [field]: value,
      },
    }))
  }

  const handleExpandAll = () => {
    if (expandedRowKeys.length === animals.length) {
      setExpandedRowKeys([])
    } else {
      setExpandedRowKeys(animals.map((animal) => animal.animal_id))
    }
  }

  // Chart generation logic (keeping the same as original)
  const handleGenerateChart = () => {
    const selectedAnimals = animals.filter((a) => selectedRowKeys.includes(a.animal_id))
    if (selectedAnimals.length === 0) {
      setChartData(null)
      setChartOptions(null)
      return
    }

    try {
      const dataArray = selectedAnimals.map((animal, idx) => {
        const edit = editable[animal.animal_id] || {}
        let breed = edit.breed
        if (typeof breed === "object" && breed !== null && "value" in breed) {
          breed = breed.value
        }
        return {
          id: idx + 1,
          month_since_pregnancy: Number.parseInt(edit.monthsSincePregnancy || 0),
          month_since_calving: Number.parseInt(edit.monthsSinceCalving || 0),
          avg_lpd_of_month: Number.parseFloat(edit.milkProduction || 0),
          date_of_avg_lpd: edit.milkProductionDate ? new Date(edit.milkProductionDate) : new Date(),
          data_collection_date: edit.dataCollectionDate ? new Date(edit.dataCollectionDate) : new Date(),
          bcs: Number.parseInt(edit.bodyConditionScore || 3),
          breed: breed || "HF",
          calvings: Number.parseInt(edit.calvingNumber || 1),
          age: Number.parseInt(edit.age || 3),
        }
      })

      // Chart generation logic (same as original)
      const allPlotData = dataArray.map((data, index) => {
        const plotData = getPlotPoints(data)
        return {
          ...plotData,
          cattleId: data.id,
        }
      })

      // Rest of chart generation logic remains the same...
      // (I'm keeping this abbreviated for space, but the full logic would be here)
    } catch (err) {
      setChartData(null)
      setChartOptions(null)
      alert(err.message || "Error generating chart")
    }
  }

  // Farmer table columns
  const farmerTableColumns = [
    {
      title: "Field",
      dataIndex: "field",
      key: "field",
      width: 160,
      render: (text) => <span style={{ fontWeight: 600, color: "#1890ff" }}>{text}</span>,
    },
    {
      title: "Value",
      dataIndex: "value",
      key: "value",
      render: (text) => <span style={{ fontWeight: 500, color: "#23272f" }}>{text}</span>,
    },
  ]

  const getFarmerTableData = (farmer) => [
    { key: "name", field: "Name", value: farmer.customer_name || "-" },
    { key: "mobile", field: "Mobile", value: farmer.mobile_number || "-" },
    { key: "village", field: "Village", value: farmer.village || "-" },
    { key: "taluk", field: "Taluk", value: farmer.taluk || "-" },
    { key: "farmerid", field: "Farmer ID", value: farmer.customer_visual_id || "-" },
  ]

  // Animals table columns
  const animalColumns = [
    {
      title: "Image",
      dataIndex: "image",
      key: "image",
      width: 80,
      render: (_, record) => {
        let imgUrl = animalImages[record.animal_id]
        if (!imgUrl && record.document_information?.url) {
          imgUrl = `https://ahs3.krushal.in/${record.document_information.url}`
        }
        if (!imgUrl) imgUrl = placeholderImg

        return <Avatar size={60} src={imgUrl} icon={<UserOutlined />} style={{ border: "2px solid #f0f0f0" }} />
      },
    },
    {
      title: "Animal Details",
      key: "details",
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 600, fontSize: 16, color: "#23272f", marginBottom: 4 }}>
            {getDisplayValue(record.animal_name) || getDisplayValue(record.animal_visual_id) || record.animal_id}
          </div>
          <div style={{ color: "#1890ff", fontWeight: 500, marginBottom: 2 }}>
            {getDisplayValue(record.animal_type) || "-"}
          </div>
          <div style={{ color: "#888", fontSize: 14 }}>Age: {getDisplayValue(record.animal_age_1) || "-"}</div>
        </div>
      ),
    },
    {
      title: "Breed",
      dataIndex: "animal_breed_1",
      key: "breed",
      width: 100,
      render: (text) => <Tag color="blue">{getDisplayValue(text) || "-"}</Tag>,
    },
    {
      title: "Ear Tag",
      dataIndex: "ear_tag_1",
      key: "ear_tag",
      width: 100,
      render: (text) => getDisplayValue(text) || "-",
    },
    {
      title: "Health Score",
      dataIndex: "health_score",
      key: "health_score",
      width: 120,
      render: (text) => {
        const score = getDisplayValue(text)
        if (!score) return "-"
        const color = score > 80 ? "green" : score > 60 ? "orange" : "red"
        return <Tag color={color}>{score}</Tag>
      },
    },
    {
      title: "Plan",
      dataIndex: "subscription_plan_1",
      key: "plan",
      width: 120,
      render: (text) => <Tag color="purple">{getDisplayValue(text) || "-"}</Tag>,
    },
  ]

  // Expandable row render
  const expandedRowRender = (record) => {
    const animalId = record.animal_id
    const editData = editable[animalId] || {}

    return (
      <div style={{ padding: "16px 24px", background: "#fafafa", borderRadius: 8 }}>
        <div style={{ display: "grid", gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))", gap: 16 }}>
          {/* Basic Info */}
          <div>
            <h4 style={{ marginBottom: 16, color: "#1890ff" }}>Basic Information</h4>
            <Space direction="vertical" size={12} style={{ width: "100%" }}>
              <div>
                <label style={{ display: "block", marginBottom: 4, fontWeight: 500 }}>Cattle Type:</label>
                <Select
                  value={editData.cattleType}
                  onChange={(value) => handleEditChange(animalId, "cattleType", value)}
                  style={{ width: "100%" }}
                >
                  {CATTLE_TYPES.map((opt) => (
                    <Option key={opt.value} value={opt.value}>
                      {opt.label}
                    </Option>
                  ))}
                </Select>
              </div>

              <div>
                <label style={{ display: "block", marginBottom: 4, fontWeight: 500 }}>Breed:</label>
                <Select
                  value={editData.breed}
                  onChange={(value) => handleEditChange(animalId, "breed", value)}
                  style={{ width: "100%" }}
                >
                  {BREEDS.map((opt) => (
                    <Option key={opt.value} value={opt.value}>
                      {opt.label}
                    </Option>
                  ))}
                </Select>
              </div>

              <div>
                <label style={{ display: "block", marginBottom: 4, fontWeight: 500 }}>Age (years):</label>
                <InputNumber
                  min={2}
                  max={15}
                  value={editData.age}
                  onChange={(value) => handleEditChange(animalId, "age", value)}
                  style={{ width: "100%" }}
                />
              </div>

              <div>
                <label style={{ display: "block", marginBottom: 4, fontWeight: 500 }}>Calving Number:</label>
                <Select
                  value={editData.calvingNumber}
                  onChange={(value) => handleEditChange(animalId, "calvingNumber", value)}
                  style={{ width: "100%" }}
                >
                  {CALVINGS.map((num) => (
                    <Option key={num} value={num}>
                      {num}
                    </Option>
                  ))}
                </Select>
              </div>
            </Space>
          </div>

          {/* Health & Production */}
          <div>
            <h4 style={{ marginBottom: 16, color: "#1890ff" }}>Health & Production</h4>
            <Space direction="vertical" size={12} style={{ width: "100%" }}>
              <div>
                <label style={{ display: "block", marginBottom: 4, fontWeight: 500 }}>Body Condition Score:</label>
                <Select
                  value={editData.bodyConditionScore}
                  onChange={(value) => handleEditChange(animalId, "bodyConditionScore", value)}
                  style={{ width: "100%" }}
                >
                  {BCS.map((opt) => (
                    <Option key={opt.value} value={opt.value}>
                      {opt.label}
                    </Option>
                  ))}
                </Select>
              </div>

              <div>
                <label style={{ display: "block", marginBottom: 4, fontWeight: 500 }}>Months Since Calving:</label>
                <InputNumber
                  min={0}
                  max={120}
                  value={editData.monthsSinceCalving}
                  onChange={(value) => handleEditChange(animalId, "monthsSinceCalving", value)}
                  style={{ width: "100%" }}
                />
              </div>

              <div>
                <label style={{ display: "block", marginBottom: 4, fontWeight: 500 }}>Months Since Pregnancy:</label>
                <InputNumber
                  min={0}
                  max={120}
                  value={editData.monthsSincePregnancy}
                  onChange={(value) => handleEditChange(animalId, "monthsSincePregnancy", value)}
                  style={{ width: "100%" }}
                />
              </div>

              <div>
                <label style={{ display: "block", marginBottom: 4, fontWeight: 500 }}>Avg. Daily Milk (L):</label>
                <InputNumber
                  min={0}
                  step={0.1}
                  value={editData.milkProduction}
                  onChange={(value) => handleEditChange(animalId, "milkProduction", value)}
                  style={{ width: "100%" }}
                />
              </div>
            </Space>
          </div>

          {/* Dates */}
          <div>
            <h4 style={{ marginBottom: 16, color: "#1890ff" }}>Important Dates</h4>
            <Space direction="vertical" size={12} style={{ width: "100%" }}>
              <div>
                <label style={{ display: "block", marginBottom: 4, fontWeight: 500 }}>Data Collection Date:</label>
                <DatePicker
                  showTime
                  value={editData.dataCollectionDate ? dayjs(editData.dataCollectionDate) : null}
                  onChange={(date) => handleEditChange(animalId, "dataCollectionDate", date ? date.format() : "")}
                  style={{ width: "100%" }}
                />
              </div>

              <div>
                <label style={{ display: "block", marginBottom: 4, fontWeight: 500 }}>Milk Production Date:</label>
                <DatePicker
                  value={editData.milkProductionDate ? dayjs(editData.milkProductionDate) : null}
                  onChange={(date) =>
                    handleEditChange(animalId, "milkProductionDate", date ? date.format("YYYY-MM-DD") : "")
                  }
                  style={{ width: "100%" }}
                />
              </div>
            </Space>
          </div>
        </div>
      </div>
    )
  }

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    getCheckboxProps: (record) => ({
      name: record.animal_name,
    }),
  }

  return (
    <div
      style={{
        padding: "24px",
        background: "#f5f7fa",
        minHeight: "100vh",
        fontFamily: "Inter, Arial, sans-serif",
      }}
    >
      {/* Farmer Information */}
      {farmer && (
        <div style={{ marginBottom: 24 }}>
          <Table
            columns={farmerTableColumns}
            dataSource={getFarmerTableData(farmer)}
            pagination={false}
            bordered
            size="middle"
            style={{
              borderRadius: 8,
              boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
              background: "#fff",
              maxWidth: 600,
            }}
          />
        </div>
      )}

      {/* Action Buttons */}
      <div style={{ marginBottom: 16 }}>
        <Space size={12}>
          <Button
            type="default"
            icon={expandedRowKeys.length === animals.length ? <CompressOutlined /> : <ExpandAltOutlined />}
            onClick={handleExpandAll}
          >
            {expandedRowKeys.length === animals.length ? "Collapse All" : "Expand All"}
          </Button>

          <Button
            type="primary"
            icon={<BarChartOutlined />}
            onClick={handleGenerateChart}
            disabled={selectedRowKeys.length === 0}
          >
            Generate Chart ({selectedRowKeys.length} selected)
          </Button>
        </Space>
      </div>

      {/* Animals Table */}
      <div style={{ display: "flex", gap: 24, alignItems: "flex-start" }}>
        <div style={{ flex: chartData ? "0 0 60%" : "1 1 100%" }}>
          <Table
            columns={animalColumns}
            dataSource={animals}
            rowKey="animal_id"
            loading={loading}
            rowSelection={rowSelection}
            expandable={{
              expandedRowRender,
              expandedRowKeys,
              onExpandedRowsChange: setExpandedRowKeys,
              expandIcon: ({ expanded, onExpand, record }) => (
                <Button
                  type="text"
                  size="small"
                  icon={<EyeOutlined />}
                  onClick={(e) => onExpand(record, e)}
                  style={{
                    color: expanded ? "#1890ff" : "#666",
                    transform: expanded ? "rotate(180deg)" : "rotate(0deg)",
                    transition: "all 0.3s",
                  }}
                />
              ),
            }}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} animals`,
            }}
            style={{
              background: "#fff",
              borderRadius: 8,
              boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
            }}
            scroll={{ x: 800 }}
          />
        </div>

        {/* Chart */}
        {chartData && chartOptions && (
          <div
            style={{
              flex: "0 0 40%",
              minWidth: 400,
              background: "#fff",
              borderRadius: 8,
              boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
              padding: 16,
            }}
          >
            <div style={{ height: 400 }}>
              <Line data={chartData} options={chartOptions} />
            </div>
          </div>
        )}
      </div>

      {error && <Alert type="error" message={error} showIcon style={{ marginTop: 16 }} />}
    </div>
  )
}

export default FarmerAnimals

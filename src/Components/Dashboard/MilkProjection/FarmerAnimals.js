"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useLocation } from "react-router-dom"
import { Spin, Alert, Table, Select, InputNumber, Button, Space, Image, Tag, Card, Row, Col, Statistic } from "antd"
import {
  UserOutlined,
  PhoneOutlined,
  HomeOutlined,
  EnvironmentOutlined,
  IdcardOutlined,
  EditOutlined,
  SaveOutlined,
  <PERSON>Outlined,
  <PERSON><PERSON>hartOutlined,
  TeamOutlined,
} from "@ant-design/icons"
import { useSelector } from "react-redux"
import { fetchAnimalsForFarmer } from "./api"
import { Line } from "react-chartjs-2"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js"
import annotationPlugin from "chartjs-plugin-annotation"
import { getPlotPoints, month_name } from "./plot"

const { Option } = Select

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, annotationPlugin)

const placeholderImg = "https://cdn-icons-png.flaticon.com/512/616/616408.png"

// Helper to get a displayable value from a field
function getDisplayValue(value) {
  if (value === null || value === undefined || value === "") return ""
  if (typeof value === "object") {
    if (typeof value.en === "string") return value.en
    if (typeof value.mr === "string") return value.mr
    if (typeof value.value === "string") return value.value
    if (typeof value.label === "string") return value.label
    if (Array.isArray(value)) return value.map((v) => String(v)).join(", ")
    const firstString = Object.values(value).find((v) => typeof v === "string")
    return firstString || ""
  }
  return String(value)
}

const getS3ImageUrl = async (animal_document_id, token) => {
  try {
    const response = await fetch(`/api/document?document_id=${animal_document_id}`, {
      headers: { token: token },
    })
    if (!response.ok) return null
    const data = await response.json()
    if (data?.data?.url) return data.data.url
    if (data?.data?.doc_key) return data.data.doc_key
    return null
  } catch {
    return null
  }
}

const CATTLE_TYPES = [
  { value: "cow", label: "Cow" },
  { value: "buffalo", label: "Buffalo" },
]

const BREEDS = [
  { value: "HF", label: "HF" },
  { value: "Jersey", label: "Jersey" },
  { value: "Other", label: "Other" },
]

const CALVINGS = [1, 2, 3, 4, 5, 6, 7]

const BCS = [
  { value: "1.0", label: "1.0 - Emaciated" },
  { value: "1.5", label: "1.5 - Very Thin" },
  { value: "2.0", label: "2.0 - Thin" },
  { value: "2.5", label: "2.5 - Slightly Thin" },
  { value: "3.0", label: "3.0 - Good" },
  { value: "3.5", label: "3.5 - Above Average" },
  { value: "4.0", label: "4.0 - Fat" },
  { value: "4.5", label: "4.5 - Very Fat" },
  { value: "5.0", label: "5.0 - Obese" },
]

function getInitialEditable(animal) {
  let breed = getDisplayValue(animal.animal_breed_1)
  if (breed !== "HF" && breed !== "Jersey") {
    breed = "HF"
  }
  return {
    cattleType: getDisplayValue(animal.animal_type) || "cow",
    breed,
    calvingNumber: getDisplayValue(animal.calving_number) || 1,
    age: getDisplayValue(animal.animal_age_1) || "",
    dataCollectionDate: "",
    monthsSinceCalving: getDisplayValue(animal.months_since_calving) || "",
    monthsSincePregnancy: getDisplayValue(animal.months_since_pregnancy) || "",
    bodyConditionScore: getDisplayValue(animal.body_condition_score) || "3.0",
    milkProduction: getDisplayValue(animal.milk_production) || "",
    milkProductionDate: "",
  }
}

const FarmerAnimals = () => {
  const { farmerId } = useParams()
  const location = useLocation()
  const farmer = location.state?.farmer
  const userToken = useSelector((state) => state.user.userToken)

  const [animals, setAnimals] = useState([])
  const [selected, setSelected] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [animalImages, setAnimalImages] = useState({})
  const [editable, setEditable] = useState({})
  const [editingKey, setEditingKey] = useState("")
  const [chartData, setChartData] = useState(null)
  const [chartOptions, setChartOptions] = useState(null)

  useEffect(() => {
    const fetchAnimals = async () => {
      if (!userToken || !farmerId) return
      setLoading(true)
      setError(null)
      try {
        const animalsData = await fetchAnimalsForFarmer(userToken, farmerId)
        setAnimals(animalsData)
      } catch (err) {
        setError("Failed to fetch animals.")
        setAnimals([])
      } finally {
        setLoading(false)
      }
    }
    fetchAnimals()
  }, [userToken, farmerId])

  useEffect(() => {
    const fetchImages = async () => {
      if (!userToken) return
      const promises = animals.map(async (animal) => {
        if (animal.animal_document_id) {
          const url = await getS3ImageUrl(animal.animal_document_id, userToken.accessToken)
          return { animal_id: animal.animal_id, url }
        }
        return null
      })
      const results = await Promise.all(promises)
      const imageMap = {}
      results.forEach((res) => {
        if (res && res.url) imageMap[res.animal_id] = res.url
      })
      setAnimalImages(imageMap)
    }
    if (animals.length > 0) fetchImages()
  }, [animals, userToken])

  useEffect(() => {
    if (animals.length > 0) {
      const initial = {}
      animals.forEach((animal) => {
        initial[animal.animal_id] = getInitialEditable(animal)
      })
      setEditable(initial)
    }
  }, [animals])

  const handleEditChange = (animalId, field, value) => {
    setEditable((prev) => ({
      ...prev,
      [animalId]: {
        ...prev[animalId],
        [field]: value,
      },
    }))
  }

  const isEditing = (record) => record.animal_id === editingKey
  const edit = (record) => setEditingKey(record.animal_id)
  const cancel = () => setEditingKey("")
  const save = async () => {
    try {
      setEditingKey("")
      // API call to save data would go here
    } catch (errInfo) {
      console.log("Validate Failed:", errInfo)
    }
  }

  // Farmer table configuration
  const farmerTableColumns = [
    {
      title: "Field",
      dataIndex: "field",
      key: "field",
      width: 150,
      render: (text, record) => (
        <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
          {record.icon}
          <span style={{ fontWeight: 600, color: "#1890ff" }}>{text}</span>
        </div>
      ),
    },
    {
      title: "Value",
      dataIndex: "value",
      key: "value",
      render: (text) => <span style={{ fontWeight: 500, color: "#23272f" }}>{text}</span>,
    },
  ]

  const getFarmerTableData = (farmer) => [
    {
      key: "name",
      field: "Name",
      value: farmer.customer_name || "-",
      icon: <UserOutlined style={{ color: "#1890ff" }} />,
    },
    {
      key: "mobile",
      field: "Mobile",
      value: farmer.mobile_number || "-",
      icon: <PhoneOutlined style={{ color: "#52c41a" }} />,
    },
    {
      key: "village",
      field: "Village",
      value: farmer.village || "-",
      icon: <HomeOutlined style={{ color: "#fa8c16" }} />,
    },
    {
      key: "taluk",
      field: "Taluk",
      value: farmer.taluk || "-",
      icon: <EnvironmentOutlined style={{ color: "#722ed1" }} />,
    },
    {
      key: "farmerId",
      field: "Farmer ID",
      value: farmer.customer_visual_id || "-",
      icon: <IdcardOutlined style={{ color: "#eb2f96" }} />,
    },
  ]

  // Animal table columns configuration
  const getAnimalColumns = () => [
    {
      title: "Image",
      dataIndex: "image",
      key: "image",
      width: 80,
      render: (_, record) => {
        let imgUrl = animalImages[record.animal_id]
        if (!imgUrl && record.document_information?.url) {
          imgUrl = `https://ahs3.krushal.in/${record.document_information.url}`
        }
        if (!imgUrl) imgUrl = placeholderImg

        return (
          <Image
            width={60}
            height={60}
            src={imgUrl || "/placeholder.svg"}
            style={{ borderRadius: "8px", objectFit: "cover" }}
            fallback={placeholderImg}
          />
        )
      },
    },
    {
      title: "Animal Info",
      dataIndex: "info",
      key: "info",
      width: 200,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 600, fontSize: "14px", marginBottom: 4 }}>
            {getDisplayValue(record.animal_name) || getDisplayValue(record.animal_visual_id) || record.animal_id}
          </div>
          <div style={{ color: "#666", fontSize: "12px" }}>{getDisplayValue(record.animal_type) || "-"}</div>
          <div style={{ color: "#666", fontSize: "12px" }}>Age: {getDisplayValue(record.animal_age_1) || "-"}</div>
        </div>
      ),
    },
    {
      title: "Breed",
      dataIndex: "breed",
      key: "breed",
      width: 120,
      render: (_, record) => {
        const editing = isEditing(record)
        return editing ? (
          <Select
            value={editable[record.animal_id]?.breed || "HF"}
            onChange={(value) => handleEditChange(record.animal_id, "breed", value)}
            style={{ width: "100%" }}
            size="small"
          >
            {BREEDS.map((opt) => (
              <Option key={opt.value} value={opt.value}>
                {opt.label}
              </Option>
            ))}
          </Select>
        ) : (
          <Tag color="blue">{editable[record.animal_id]?.breed || "HF"}</Tag>
        )
      },
    },
    {
      title: "Cattle Type",
      dataIndex: "cattleType",
      key: "cattleType",
      width: 120,
      render: (_, record) => {
        const editing = isEditing(record)
        return editing ? (
          <Select
            value={editable[record.animal_id]?.cattleType || "cow"}
            onChange={(value) => handleEditChange(record.animal_id, "cattleType", value)}
            style={{ width: "100%" }}
            size="small"
          >
            {CATTLE_TYPES.map((opt) => (
              <Option key={opt.value} value={opt.value}>
                {opt.label}
              </Option>
            ))}
          </Select>
        ) : (
          <Tag color="green">{editable[record.animal_id]?.cattleType || "cow"}</Tag>
        )
      },
    },
    {
      title: "Age",
      dataIndex: "age",
      key: "age",
      width: 80,
      render: (_, record) => {
        const editing = isEditing(record)
        return editing ? (
          <InputNumber
            min={2}
            max={15}
            value={editable[record.animal_id]?.age || ""}
            onChange={(value) => handleEditChange(record.animal_id, "age", value)}
            style={{ width: "100%" }}
            size="small"
          />
        ) : (
          <span>{editable[record.animal_id]?.age || "-"}</span>
        )
      },
    },
    {
      title: "Calving #",
      dataIndex: "calvingNumber",
      key: "calvingNumber",
      width: 100,
      render: (_, record) => {
        const editing = isEditing(record)
        return editing ? (
          <Select
            value={editable[record.animal_id]?.calvingNumber || 1}
            onChange={(value) => handleEditChange(record.animal_id, "calvingNumber", value)}
            style={{ width: "100%" }}
            size="small"
          >
            {CALVINGS.map((num) => (
              <Option key={num} value={num}>
                {num}
              </Option>
            ))}
          </Select>
        ) : (
          <span>{editable[record.animal_id]?.calvingNumber || 1}</span>
        )
      },
    },
    {
      title: "BCS",
      dataIndex: "bodyConditionScore",
      key: "bodyConditionScore",
      width: 120,
      render: (_, record) => {
        const editing = isEditing(record)
        const bcsValue = editable[record.animal_id]?.bodyConditionScore || "3.0"
        const bcsLabel = BCS.find((b) => b.value === bcsValue)?.label || bcsValue

        return editing ? (
          <Select
            value={bcsValue}
            onChange={(value) => handleEditChange(record.animal_id, "bodyConditionScore", value)}
            style={{ width: "100%" }}
            size="small"
          >
            {BCS.map((opt) => (
              <Option key={opt.value} value={opt.value}>
                {opt.label}
              </Option>
            ))}
          </Select>
        ) : (
          <Tag color={Number.parseFloat(bcsValue) >= 3.0 ? "green" : "orange"}>{bcsLabel}</Tag>
        )
      },
    },
    {
      title: "Milk Production (L)",
      dataIndex: "milkProduction",
      key: "milkProduction",
      width: 150,
      render: (_, record) => {
        const editing = isEditing(record)
        return editing ? (
          <InputNumber
            min={0}
            step={0.1}
            value={editable[record.animal_id]?.milkProduction || ""}
            onChange={(value) => handleEditChange(record.animal_id, "milkProduction", value)}
            style={{ width: "100%" }}
            size="small"
          />
        ) : (
          <span>{editable[record.animal_id]?.milkProduction || "-"} L</span>
        )
      },
    },
    {
      title: "Months Since Calving",
      dataIndex: "monthsSinceCalving",
      key: "monthsSinceCalving",
      width: 150,
      render: (_, record) => {
        const editing = isEditing(record)
        return editing ? (
          <InputNumber
            min={0}
            max={120}
            value={editable[record.animal_id]?.monthsSinceCalving || ""}
            onChange={(value) => handleEditChange(record.animal_id, "monthsSinceCalving", value)}
            style={{ width: "100%" }}
            size="small"
            placeholder="Months"
          />
        ) : (
          <span>{editable[record.animal_id]?.monthsSinceCalving || "-"}</span>
        )
      },
    },
    {
      title: "Months Since Pregnancy",
      dataIndex: "monthsSincePregnancy",
      key: "monthsSincePregnancy",
      width: 160,
      render: (_, record) => {
        const editing = isEditing(record)
        return editing ? (
          <InputNumber
            min={0}
            max={120}
            value={editable[record.animal_id]?.monthsSincePregnancy || ""}
            onChange={(value) => handleEditChange(record.animal_id, "monthsSincePregnancy", value)}
            style={{ width: "100%" }}
            size="small"
            placeholder="Months"
          />
        ) : (
          <span>{editable[record.animal_id]?.monthsSincePregnancy || "-"}</span>
        )
      },
    },
    {
      title: "Data Collection Date",
      dataIndex: "dataCollectionDate",
      key: "dataCollectionDate",
      width: 180,
      render: (_, record) => {
        const editing = isEditing(record)
        return editing ? (
          <input
            type="datetime-local"
            value={editable[record.animal_id]?.dataCollectionDate || ""}
            onChange={(e) => handleEditChange(record.animal_id, "dataCollectionDate", e.target.value)}
            style={{
              width: "100%",
              padding: "4px 8px",
              border: "1px solid #d9d9d9",
              borderRadius: "6px",
              fontSize: "12px",
            }}
          />
        ) : (
          <span>
            {editable[record.animal_id]?.dataCollectionDate
              ? new Date(editable[record.animal_id].dataCollectionDate).toLocaleDateString()
              : "-"}
          </span>
        )
      },
    },
    {
      title: "Milk Production Date",
      dataIndex: "milkProductionDate",
      key: "milkProductionDate",
      width: 160,
      render: (_, record) => {
        const editing = isEditing(record)
        return editing ? (
          <input
            type="date"
            value={editable[record.animal_id]?.milkProductionDate || ""}
            onChange={(e) => handleEditChange(record.animal_id, "milkProductionDate", e.target.value)}
            style={{
              width: "100%",
              padding: "4px 8px",
              border: "1px solid #d9d9d9",
              borderRadius: "6px",
              fontSize: "12px",
            }}
          />
        ) : (
          <span>{editable[record.animal_id]?.milkProductionDate || "-"}</span>
        )
      },
    },
    {
      title: "Actions",
      dataIndex: "actions",
      key: "actions",
      width: 120,
      render: (_, record) => {
        const editing = isEditing(record)
        return editing ? (
          <Space>
            <Button icon={<SaveOutlined />} onClick={() => save()} size="small" type="primary" />
            <Button icon={<CloseOutlined />} onClick={cancel} size="small" />
          </Space>
        ) : (
          <Button icon={<EditOutlined />} onClick={() => edit(record)} size="small" disabled={editingKey !== ""}>
            Edit
          </Button>
        )
      },
    },
  ]

  // Chart generation function (keeping the same logic as original)
  const [alertInfo, setAlertInfo] = useState(null);

  const handleGenerateChart = () => {
    console.log('Generate chart clicked, selected animals:', selected.length);
    const selectedAnimals = animals.filter((a) => selected.includes(a.animal_id))
    console.log('Filtered selected animals:', selectedAnimals.length);

    // Clear any previous alerts
    setAlertInfo(null);

    if (selectedAnimals.length === 0) {
      console.log('No animals selected, clearing chart');
      setAlertInfo({
        type: 'warning',
        message: 'No Animals Selected',
        description: 'Please select at least one animal to generate the milk production chart.',
        showIcon: true
      });
      setChartData(null)
      setChartOptions(null)
      return
    }

    // Validate required fields for each selected animal
    const validationErrors = [];
    selectedAnimals.forEach((animal) => {
      const edit = editable[animal.animal_id] || {};

      // Get the best identifier for the animal (ear-tag > visual-id > animal_id)
      const animalIdentifier = edit.earTag ||
                              animal.ear_tag ||
                              edit.visualId ||
                              animal.animal_visual_id ||
                              animal.animal_id;

      const animalName = edit.earTag || animal.ear_tag
        ? `Animal (Ear Tag: ${animalIdentifier})`
        : edit.visualId || animal.animal_visual_id
        ? `Animal (Visual ID: ${animalIdentifier})`
        : `Animal #${animalIdentifier}`;

      if (!edit.monthsSinceCalving && edit.monthsSinceCalving !== 0) {
        validationErrors.push(`${animalName}: Months Since Calving is required`);
      }

      if (!edit.monthsSincePregnancy && edit.monthsSincePregnancy !== 0) {
        validationErrors.push(`${animalName}: Months Since Pregnancy is required`);
      }

      if (!edit.dataCollectionDate) {
        validationErrors.push(`${animalName}: Data Collection Date is required`);
      }

      if (!edit.milkProduction && edit.milkProduction !== 0) {
        validationErrors.push(`${animalName}: Average Milk Production is required`);
      }

      if (!edit.milkProductionDate) {
        validationErrors.push(`${animalName}: Milk Production Date is required`);
      }
    });

    if (validationErrors.length > 0) {
      // Group errors by animal for better spacing
      const errorsByAnimal = {};
      validationErrors.forEach(error => {
        const animalMatch = error.match(/^(Animal.*?):/);
        const animalName = animalMatch ? animalMatch[1] : 'Unknown Animal';
        const fieldError = error.substring(animalName.length + 2); // Remove "AnimalName: "

        if (!errorsByAnimal[animalName]) {
          errorsByAnimal[animalName] = [];
        }
        errorsByAnimal[animalName].push(fieldError);
      });

      setAlertInfo({
        type: 'error',
        message: 'Missing Required Information',
        description: (
          <div>
            <p style={{ marginBottom: 12 }}>Please fill in the following required fields before generating the chart:</p>
            <div style={{ marginTop: 8, marginBottom: 0 }}>
              {Object.entries(errorsByAnimal).map(([animalName, errors], animalIndex) => (
                <div key={animalIndex} style={{
                  marginBottom: animalIndex === Object.keys(errorsByAnimal).length - 1 ? 0 : 16,
                  padding: 12,
                  backgroundColor: '#fff2f0',
                  borderRadius: 6,
                  border: '1px solid #ffccc7'
                }}>
                  <div style={{
                    fontWeight: 600,
                    color: '#cf1322',
                    marginBottom: 8,
                    fontSize: 14
                  }}>
                    {animalName}
                  </div>
                  <ul style={{
                    margin: 0,
                    paddingLeft: 20,
                    listStyleType: 'disc'
                  }}>
                    {errors.map((error, errorIndex) => (
                      <li key={errorIndex} style={{
                        marginBottom: errorIndex === errors.length - 1 ? 0 : 4,
                        color: '#8c8c8c',
                        fontSize: 13
                      }}>
                        {error}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        ),
        showIcon: true
      });
      return;
    }

    try {
      const dataArray = selectedAnimals.map((animal, idx) => {
        const edit = editable[animal.animal_id] || {}
        let breed = edit.breed
        if (typeof breed === "object" && breed !== null && "value" in breed) {
          breed = breed.value
        }
        const animalData = {
          id: idx + 1,
          month_since_pregnancy: Number.parseInt(edit.monthsSincePregnancy || 0),
          month_since_calving: Number.parseInt(edit.monthsSinceCalving || 0),
          avg_lpd_of_month: Number.parseFloat(edit.milkProduction || 0),
          date_of_avg_lpd: edit.milkProductionDate ? new Date(edit.milkProductionDate) : new Date(),
          data_collection_date: edit.dataCollectionDate ? new Date(edit.dataCollectionDate) : new Date(),
          bcs: Number.parseInt(edit.bodyConditionScore || 3),
          breed: breed || "HF",
          calvings: Number.parseInt(edit.calvingNumber || 1),
          age: Number.parseInt(edit.age || 3),
        }
        console.log('Animal data for chart:', animalData);
        return animalData;
      })

      console.log('Data array for chart generation:', dataArray);

      // Chart generation logic using the same approach as your working code
      const allPlotData = dataArray.map((data) => {
        console.log('Generating plot points for animal:', data.id);
        const plotData = getPlotPoints(data)
        console.log('Plot data generated:', plotData);
        return {
          ...plotData,
          cattleId: data.id,
        }
      })

      console.log('All plot data:', allPlotData);

      // Calculate chart bounds using the same logic as your working code
      let minStartYear0 = Math.min(...allPlotData.map(plot => plot.start_year)) || new Date().getFullYear();
      let maxStartYear0 = Math.max(...allPlotData.map(plot => plot.start_year)) || new Date().getFullYear();

      let minStartYearMonths = 0;
      let maxStartYearMonths = 0;

      allPlotData.forEach(plot => {
        if (plot.start_year === minStartYear0) {
          minStartYearMonths = Math.min(...plot.x);
        }
        if (plot.start_year === maxStartYear0) {
          maxStartYearMonths = Math.max(...plot.x);
        }
      });

      // Create month labels using the same logic as your working code
      const month_labels = Array.from({length: 33}, (_, i) => {
        const monthNum = minStartYearMonths + i;
        const monthIdx = (monthNum % 12) === 0 ? 12 : monthNum % 12;
        return {
          key: `${monthIdx % 12 === 0 ? minStartYear0++ : minStartYear0}-${monthIdx}-${month_name[monthIdx]}`,
          name: month_name[monthIdx],
          month: monthNum
        };
      });

      console.log('Month labels:', month_labels);

      // Helper function to get days in a month
      const getDaysInMonth = (monthNum, year) => {
        const month = ((monthNum - 1) % 12) + 1; // Convert to 1-12 range
        return new Date(year, month, 0).getDate();
      };

      // Generate chart datasets using the same logic as your working code
      const lineColors = [
        '#3b82f6',
        '#ef4444',
        '#22c55e',
        '#f97316',
        '#8b5cf6',
        '#06b6d4',
        '#ec4899',
        '#f59e0b',
      ];

      const datasets = [];

      // Create daily production datasets (original curves)
      allPlotData.forEach((plotData, index) => {
        console.log(`Creating daily dataset for Animal ${plotData.cattleId}`);

        datasets.push({
          label: `Animal #${plotData.cattleId} (Daily)`,
          data: month_labels.map(label => {
            const value = plotData.map[label.key];
            return typeof value === 'object' ? value.lpd : value;
          }),
          borderColor: lineColors[index % lineColors.length],
          backgroundColor: 'transparent',
          borderWidth: 2,
          fill: false,
          tension: 0.4,
          pointRadius: month_labels.map(label => {
            const value = plotData.map[label.key];
            return value && typeof value === 'object' ? value.pointRadius : 3;
          }),
          pointHoverRadius: month_labels.map(() => 8),
          pointStyle: month_labels.map(label => {
            const value = plotData.map[label.key];
            return value && typeof value === 'object' ? value.pointStyle : 'circle';
          }),
          pointBackgroundColor: month_labels.map(label => {
            const value = plotData.map[label.key];
            return value && typeof value === 'object' ? value.color : lineColors[index % lineColors.length];
          }),
          pointBorderColor: 'white',
          pointBorderWidth: 1,
          yAxisID: 'y', // Use left Y-axis for daily production
        });
      });

      // Create monthly total production datasets (new curves)
      allPlotData.forEach((plotData, index) => {
        console.log(`Creating monthly total dataset for Animal ${plotData.cattleId}`);

        datasets.push({
          label: `Animal #${plotData.cattleId} (Monthly Total)`,
          data: month_labels.map(label => {
            const value = plotData.map[label.key];
            const dailyYield = typeof value === 'object' ? value.lpd : value;

            if (dailyYield === null || dailyYield === undefined || dailyYield === 0) {
              return 0;
            }

            // Calculate year and month from the label
            const [yearStr] = label.key.split('-');
            const year = parseInt(yearStr);
            const monthNum = label.month;

            // Get days in this specific month
            const daysInMonth = getDaysInMonth(monthNum, year);

            // Calculate total monthly production
            const monthlyTotal = dailyYield * daysInMonth;

            return monthlyTotal;
          }),
          borderColor: lineColors[index % lineColors.length],
          backgroundColor: lineColors[index % lineColors.length].replace('1)', '0.1)'),
          borderWidth: 2,
          borderDash: [5, 5], // Dashed line to distinguish from daily
          fill: false,
          tension: 0.4,
          pointRadius: 4,
          pointHoverRadius: 8,
          pointStyle: 'rect', // Square points to distinguish from daily
          pointBackgroundColor: lineColors[index % lineColors.length],
          pointBorderColor: 'white',
          pointBorderWidth: 2,
          yAxisID: 'y1', // Use right Y-axis for monthly totals
        });
      });

      console.log('Final datasets for chart:', datasets);

      // Create separate charts for each animal
      const chartDataArray = allPlotData.map((plotData, animalIndex) => {
        const animalDatasets = [
          // Daily production dataset for this animal
          {
            label: `Avg Daily Production`,
            data: month_labels.map(label => {
              const value = plotData.map[label.key];
              return typeof value === 'object' ? value.lpd : value;
            }),
            borderColor: lineColors[animalIndex % lineColors.length],
            backgroundColor: 'transparent',
            borderWidth: 2,
            fill: false,
            tension: 0.4,
            pointRadius: month_labels.map(label => {
              const value = plotData.map[label.key];
              return value && typeof value === 'object' ? value.pointRadius : 3;
            }),
            pointHoverRadius: month_labels.map(() => 8),
            pointStyle: month_labels.map(label => {
              const value = plotData.map[label.key];
              return value && typeof value === 'object' ? value.pointStyle : 'circle';
            }),
            pointBackgroundColor: month_labels.map(label => {
              const value = plotData.map[label.key];
              return value && typeof value === 'object' ? value.color : lineColors[animalIndex % lineColors.length];
            }),
            pointBorderColor: 'white',
            pointBorderWidth: 1,
            yAxisID: 'y',
          },
          // Monthly total dataset for this animal
          {
            label: `Monthly Total Production`,
            data: month_labels.map(label => {
              const value = plotData.map[label.key];
              const dailyYield = typeof value === 'object' ? value.lpd : value;

              if (dailyYield === null || dailyYield === undefined || dailyYield === 0) {
                return 0;
              }

              const [yearStr] = label.key.split('-');
              const year = parseInt(yearStr);
              const monthNum = label.month;
              const daysInMonth = getDaysInMonth(monthNum, year);
              const monthlyTotal = dailyYield * daysInMonth;

              return monthlyTotal;
            }),
            borderColor: lineColors[animalIndex % lineColors.length],
            backgroundColor: lineColors[animalIndex % lineColors.length].replace('1)', '0.1)'),
            borderWidth: 2,
            borderDash: [5, 5],
            fill: false,
            tension: 0.4,
            pointRadius: 4,
            pointHoverRadius: 8,
            pointStyle: 'rect',
            pointBackgroundColor: lineColors[animalIndex % lineColors.length],
            pointBorderColor: 'white',
            pointBorderWidth: 2,
            yAxisID: 'y1',
          }
        ];

        return {
          animalId: plotData.cattleId,
          chartData: {
            labels: month_labels.map(label => label.name),
            datasets: animalDatasets,
          },
          plotData: plotData
        };
      });

      console.log('Setting chart data array:', chartDataArray);
      setChartData(chartDataArray);

      // Create annotations using the same logic as your working code
      const currentYear = new Date().getFullYear();
      const monthsPerYear = 12;
      let minStartYear = Math.min(...allPlotData.map(plot => plot.start_year)) || currentYear;

      // Year background annotations
      const yearBackgrounds = {};
      const totalYears = Math.ceil(month_labels.length / monthsPerYear);
      for (let i = 0; i < totalYears; i++) {
        const year = minStartYear + i;
        yearBackgrounds[`year${year}`] = {
          type: 'box',
          xMin: i * monthsPerYear,
          xMax: Math.min((i + 1) * monthsPerYear - 1, month_labels.length - 1),
          yMin: 'min',
          yMax: 'max',
          backgroundColor: 'rgba(0,0,0,0)', // transparent
          borderWidth: 0,
          drawTime: 'beforeDatasetsDraw',
          label: {
            display: true,
            content: year.toString(),
            position: {x: 'start', y: 'start'},
            padding: {top: 4, left: 8},
            color: 'rgba(107, 114, 128, 0.8)',
            font: {
              size: 14,
              weight: '500',
              family: 'system-ui'
            }
          }
        };
      }

      // Create annotations for special points and year transitions
      const annotations = {};
      month_labels.forEach((label, index) => {
        // Add annotations for special points (calving, pregnancy, etc.)
        allPlotData.forEach((plotData, cattleIndex) => {
          const value = plotData.map[label.key];
          if (typeof value === 'object' && value.message && value.message !== 'Production Data') {
            // Add point annotations for special events
            if (value.message === 'Calving month') {
              annotations[`calving_${index}_${cattleIndex}`] = {
                type: 'point',
                xValue: index,
                yValue: value.lpd,
                backgroundColor: 'pink',
                borderColor: '#fff',
                borderWidth: 2,
                radius: 6,
                label: {
                  enabled: true,
                  content: `Calving: ${value.lpd.toFixed(1)}L`,
                  position: 'top',
                  backgroundColor: 'pink',
                  color: '#000',
                  font: { size: 10, weight: 'bold' },
                  padding: 4,
                  cornerRadius: 4,
                }
              };
            } else if (value.message === 'Pregnancy started') {
              annotations[`pregnancy_${index}_${cattleIndex}`] = {
                type: 'point',
                xValue: index,
                yValue: value.lpd,
                backgroundColor: '#2ef9ea',
                borderColor: '#fff',
                borderWidth: 2,
                radius: 6,
                label: {
                  enabled: true,
                  content: `Pregnancy: ${value.lpd.toFixed(1)}L`,
                  position: 'bottom',
                  backgroundColor: '#2ef9ea',
                  color: '#000',
                  font: { size: 10, weight: 'bold' },
                  padding: 4,
                  cornerRadius: 4,
                }
              };
            } else if (value.message === 'Ideal pregnancy month') {
              annotations[`ideal_pregnancy_${index}_${cattleIndex}`] = {
                type: 'point',
                xValue: index,
                yValue: value.lpd,
                backgroundColor: 'blue',
                borderColor: '#fff',
                borderWidth: 2,
                radius: 6,
                label: {
                  enabled: true,
                  content: `Ideal: ${value.lpd.toFixed(1)}L`,
                  position: 'left',
                  backgroundColor: 'blue',
                  color: '#fff',
                  font: { size: 10, weight: 'bold' },
                  padding: 4,
                  cornerRadius: 4,
                }
              };
            } else if (value.message === 'Dry period') {
              annotations[`dry_${index}_${cattleIndex}`] = {
                type: 'point',
                xValue: index,
                yValue: value.lpd,
                backgroundColor: 'red',
                borderColor: '#fff',
                borderWidth: 2,
                radius: 6,
                label: {
                  enabled: true,
                  content: 'Dry Period',
                  position: 'top',
                  backgroundColor: 'red',
                  color: '#fff',
                  font: { size: 10, weight: 'bold' },
                  padding: 4,
                  cornerRadius: 4,
                }
              };
            }
          }
        });

        // Add year transition lines
        const month = label.month;
        if (month % 12 === 1 && index > 0) {
          annotations[`yearTransition${index}`] = {
            type: 'line',
            xMin: index,
            xMax: index,
            borderColor: 'rgba(75, 85, 99, 1)',
            borderWidth: 2,
          };
        }
      });

      // Create chart options for individual animal charts
      const createChartOptions = (animalId) => ({
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          title: {
            display: true,
            text: `Animal #${animalId} - Milk Production Forecast ${currentYear}`,
            font: { size: 18, weight: 'bold' },
            color: '#1f2937',
          },
          legend: {
            display: true,
            position: 'top',
            labels: {
              usePointStyle: true,
              padding: 20,
              font: { size: 12 },
            },
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                const value = context.parsed.y;
                if (value === null || value === undefined) return null;

                const datasetLabel = context.dataset.label;
                const isMonthlyTotal = datasetLabel.includes('Monthly Total');
                const animalIndex = Math.floor(context.datasetIndex / 2); // Since we have 2 datasets per animal
                const plotData = allPlotData[animalIndex];
                const label = month_labels[context.dataIndex];

                const mapValue = plotData.map[label.key];
                const message = typeof mapValue === 'object' ? mapValue.message : 'Production Data';

                if (isMonthlyTotal) {
                  return [
                    `${datasetLabel}: ${value.toFixed(0)} L/month`,
                    `Status: ${message}`
                  ];
                } else {
                  return [
                    `${datasetLabel}: ${value.toFixed(2)} L/day`,
                    `Status: ${message}`
                  ];
                }
              },
              title: function(context) {
                const label = month_labels[context[0].dataIndex];
                return `${label.name} (Month ${label.month})`;
              }
            }
          },
          annotation: {
            common: {
              drawTime: 'beforeDraw'
            },
            annotations: {
              ...yearBackgrounds,  // Year backgrounds drawn first
              ...annotations      // Point annotations drawn on top
            }
          },
        },
        scales: {
          x: {
            title: {
              display: true,
              text: 'Month',
              font: {
                size: 14,
                weight: '500',
                family: 'system-ui'
              },
              padding: {top: 15}
            },
            grid: {
              color: 'rgba(226, 232, 240, 0.6)',
              lineWidth: 1,
              drawBorder: true
            },
            ticks: {
              padding: 10,
              font: {
                size: 12,
                family: 'system-ui'
              }
            },
            offset: true,
          },
          y: {
            type: 'linear',
            display: true,
            position: 'left',
            title: {
              display: true,
              text: 'Avg Daily Production (Liters/Day)',
              font: {
                size: 14,
                weight: '500',
                family: 'system-ui'
              },
              padding: {right: 15}
            },
            grid: {
              color: 'rgba(226, 232, 240, 0.6)',
              lineWidth: 1,
              drawBorder: false,
              drawTicks: false
            },
            ticks: {
              padding: 10,
              font: {
                size: 12,
                family: 'system-ui'
              }
            },
            beginAtZero: true,
            suggestedMax: Math.ceil(Math.max(
              ...allPlotData.flatMap(plot =>
                Object.values(plot.map)
                  .map(v => typeof v === 'object' ? v.lpd : v)
                  .filter(v => typeof v === 'number')
              )
            ) * 1.1) // Add 10% buffer to max value
          },
          y1: {
            type: 'linear',
            display: true,
            position: 'right',
            title: {
              display: true,
              text: 'Monthly Total (Liters/Month)',
              font: {
                size: 14,
                weight: '500',
                family: 'system-ui'
              },
              padding: {left: 15}
            },
            grid: {
              drawOnChartArea: false, // Don't draw grid lines for right axis
            },
            ticks: {
              padding: 10,
              font: {
                size: 12,
                family: 'system-ui'
              }
            },
            beginAtZero: true,
            suggestedMax: Math.ceil(Math.max(
              ...allPlotData.flatMap(plot =>
                Object.values(plot.map)
                  .map(v => {
                    const dailyYield = typeof v === 'object' ? v.lpd : v;
                    if (dailyYield === null || dailyYield === undefined || dailyYield === 0) return 0;
                    return dailyYield * 31; // Approximate max days in month
                  })
                  .filter(v => typeof v === 'number')
              )
            ) * 1.1) // Add 10% buffer to max value
          }
        },
        layout: {
          padding: {
            top: 20,
            right: 20,
            bottom: 20,
            left: 20
          }
        }
      });

      // Set chart options for all animals
      const chartOptionsArray = chartDataArray.map(item =>
        createChartOptions(item.animalId)
      );

      console.log('Setting chart options array:', chartOptionsArray);
      setChartOptions(chartOptionsArray);
    } catch (err) {
      console.error('Error generating chart:', err);
      setChartData(null)
      setChartOptions(null)
      setAlertInfo({
        type: 'error',
        message: 'Chart Generation Failed',
        description: `An error occurred while generating the chart: ${err.message || "Unknown error"}. Please check your data and try again.`,
        showIcon: true
      });
    }
  }

  return (
    <div
      style={{
        background: "#f0f2f5",
        minHeight: "100vh",
        padding: "24px",
        fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      }}
    >
      <div style={{ maxWidth: 1400, margin: "0 auto" }}>
        {/* Header */}
        <div style={{ marginBottom: 24 }}>
          <h1
            style={{
              fontSize: 32,
              fontWeight: 700,
              color: "#1f2937",
              margin: 0,
              marginBottom: 8,
              letterSpacing: "-0.5px",
            }}
          >
            Animal Management Dashboard
          </h1>
          <p
            style={{
              fontSize: 16,
              color: "#6b7280",
              margin: 0,
            }}
          >
            Manage animals and generate milk production projections
          </p>
        </div>

        {/* Stats Cards */}
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="Total Animals"
                value={animals.length}
                prefix={<TeamOutlined />}
                valueStyle={{ color: "#1890ff" }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="Selected"
                value={selected.length}
                prefix={<UserOutlined />}
                valueStyle={{ color: "#52c41a" }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Statistic
                title="Currently Editing"
                value={editingKey ? 1 : 0}
                prefix={<EditOutlined />}
                valueStyle={{ color: "#fa8c16" }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card>
              <Button
                type="primary"
                size="large"
                icon={<BarChartOutlined />}
                onClick={handleGenerateChart}
                disabled={selected.length === 0}
                style={{ width: "100%", height: 60 }}
              >
                Generate Chart
              </Button>
            </Card>
          </Col>
        </Row>

        {/* Farmer Information */}
        {farmer && (
          <Card
            title={
              <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
                <UserOutlined style={{ color: "#1890ff" }} />
                Farmer Information
              </div>
            }
            style={{ marginBottom: 24 }}
          >
            <Table
              columns={farmerTableColumns}
              dataSource={getFarmerTableData(farmer)}
              pagination={false}
              size="small"
              showHeader={false}
            />
          </Card>
        )}

        {/* Animals Table */}
        <Card
          title={
            <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
              <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
                <TeamOutlined style={{ color: "#1890ff" }} />
                Animals ({animals.length})
              </div>
              {selected.length > 0 && <Tag color="blue">{selected.length} selected</Tag>}
            </div>
          }
          style={{ marginBottom: 24 }}
        >
          {/* Alert Messages */}
          {alertInfo && (
            <Alert
              type={alertInfo.type}
              message={alertInfo.message}
              description={alertInfo.description}
              showIcon={alertInfo.showIcon}
              closable
              onClose={() => setAlertInfo(null)}
              style={{ marginBottom: 20 }}
            />
          )}

          {loading ? (
            <div style={{ display: "flex", justifyContent: "center", alignItems: "center", minHeight: 200 }}>
              <Spin size="large" />
            </div>
          ) : error ? (
            <Alert type="error" message={error} showIcon />
          ) : animals.length === 0 ? (
            <div style={{ textAlign: "center", color: "#888", fontSize: 16, padding: 40 }}>
              No animals found for this farmer.
            </div>
          ) : (
            <Table
              columns={getAnimalColumns()}
              dataSource={animals}
              rowKey="animal_id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} animals`,
              }}
              scroll={{ x: 1600 }}
              size="small"
              rowSelection={{
                selectedRowKeys: selected,
                onChange: (selectedRowKeys) => {
                  setSelected(selectedRowKeys)
                },
                getCheckboxProps: (record) => ({
                  name: record.animal_id,
                }),
              }}
            />
          )}
        </Card>

        {/* Charts Section */}
        {chartData && chartOptions && Array.isArray(chartData) && (
          <div style={{ display: 'flex', flexDirection: 'column', gap: 24 }}>
            {/* Chart Legend - Show once at the top */}
            <Card
              title={
                <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
                  <BarChartOutlined style={{ color: "#1890ff" }} />
                  Milk Production Forecast - Chart Guide
                </div>
              }
            >
              <div style={{
                padding: 16,
                backgroundColor: '#f8fafc',
                borderRadius: 8,
                border: '1px solid #e5e7eb'
              }}>
                {/* Curve Types */}
                <div style={{ marginBottom: 12 }}>
                  <div style={{
                    fontSize: 14,
                    fontWeight: 600,
                    marginBottom: 8,
                    color: '#374151'
                  }}>
                    Curve Types:
                  </div>
                  <div style={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    gap: 20,
                    fontSize: 12
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                      <div style={{
                        width: 20,
                        height: 2,
                        backgroundColor: '#3b82f6',
                        borderRadius: 1
                      }}></div>
                      <span><strong>Solid Lines:</strong> Avg Daily Production (L/day)</span>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                      <div style={{
                        width: 20,
                        height: 2,
                        backgroundColor: '#3b82f6',
                        borderRadius: 1,
                        borderTop: '2px dashed #3b82f6',
                        backgroundColor: 'transparent'
                      }}></div>
                      <span><strong>Dashed Lines:</strong> Monthly Total (L/month)</span>
                    </div>
                  </div>
                </div>

                {/* Event Annotations */}
                <div>
                  <div style={{
                    fontSize: 14,
                    fontWeight: 600,
                    marginBottom: 8,
                    color: '#374151'
                  }}>
                    Event Annotations:
                  </div>
                  <div style={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    gap: 16,
                    fontSize: 12
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
                      <div style={{
                        width: 12,
                        height: 12,
                        borderRadius: '50%',
                        backgroundColor: 'pink',
                        border: '2px solid #fff'
                      }}></div>
                      <span>Calving</span>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
                      <div style={{
                        width: 12,
                        height: 12,
                        borderRadius: '50%',
                        backgroundColor: '#2ef9ea',
                        border: '2px solid #fff'
                      }}></div>
                      <span>Pregnancy Start</span>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
                      <div style={{
                        width: 12,
                        height: 12,
                        borderRadius: '50%',
                        backgroundColor: 'blue',
                        border: '2px solid #fff'
                      }}></div>
                      <span>Ideal Pregnancy</span>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 6 }}>
                      <div style={{
                        width: 12,
                        height: 12,
                        borderRadius: '50%',
                        backgroundColor: 'red',
                        border: '2px solid #fff'
                      }}></div>
                      <span>Dry Period</span>
                    </div>
                  </div>
                </div>
              </div>
            </Card>

            {/* Individual Animal Charts */}
            {chartData.map((animalChart, index) => (
              <Card
                key={`animal-chart-${animalChart.animalId}`}
                title={
                  <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
                    <BarChartOutlined style={{ color: "#1890ff" }} />
                    Animal #{animalChart.animalId} - Milk Production Forecast
                  </div>
                }
                style={{ marginBottom: index === chartData.length - 1 ? 0 : 16 }}
              >
                <div style={{ height: 500 }}>
                  <Line
                    data={animalChart.chartData}
                    options={chartOptions[index]}
                  />
                </div>
              </Card>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default FarmerAnimals

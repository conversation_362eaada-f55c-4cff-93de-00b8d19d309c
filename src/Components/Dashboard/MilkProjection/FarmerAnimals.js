import React, { useState, useEffect } from 'react';
import { useParams, useLocation } from 'react-router-dom';
import { Checkbox, Spin, Alert } from 'antd';
import { useSelector } from 'react-redux';
import { fetchAnimalsForFarmer } from './api';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import annotationPlugin from 'chartjs-plugin-annotation';
import { getPlotPoints } from './plot';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  annotationPlugin
);

const cardStyle = {
  background: '#fff',
  borderRadius: 10,
  boxShadow: '0 2px 8px rgba(60,72,88,0.08)',
  padding: '10px 14px',
  marginBottom: 12,
  width: '100%',
  textAlign: 'left',
  display: 'flex',
  alignItems: 'flex-start',
  position: 'relative',
  transition: 'box-shadow 0.18s, border 0.18s, transform 0.12s',
  cursor: 'pointer',
  border: '1px solid #e3e6ea',
  overflow: 'hidden',
};

const selectedCardStyle = {
  border: '2px solid #1890ff',
  boxShadow: '0 4px 16px rgba(24,144,255,0.10)',
  background: '#f6fbff',
};

const placeholderImg = 'https://cdn-icons-png.flaticon.com/512/616/616408.png';
const checkboxStyle = {
  position: 'absolute',
  top: 10,
  right: 10,
  zIndex: 2,
  transform: 'scale(1.1)',
  background: '#fff',
  borderRadius: 6,
  boxShadow: '0 1px 4px rgba(60,72,88,0.08)',
  padding: 1,
};
const contentStyle = {
  display: 'flex',
  flexDirection: 'column',
  flex: 1,
  minWidth: 0,
  padding: '10px 10px 10px 0',
};
const nameStyle = {
  fontWeight: 600,
  fontSize: 18,
  marginBottom: 2,
  color: '#23272f',
  lineHeight: 1.2,
  whiteSpace: 'nowrap',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
};
const typeStyle = {
  fontWeight: 500,
  fontSize: 15,
  color: '#1890ff',
  marginBottom: 0,
};
const ageStyle = {
  color: '#888',
  fontSize: 15,
  marginBottom: 4,
};
const detailRowStyle = {
  display: 'flex',
  alignItems: 'center',
  gap: 6,
  marginBottom: 2,
};
const detailLabelStyle = {
  fontWeight: 500,
  color: '#555',
  minWidth: 70,
  fontSize: 15,
};
const detailValueStyle = {
  fontWeight: 400,
  color: '#222',
  fontSize: 15,
  flex: 1,
  wordBreak: 'break-word',
};

// Helper to get a displayable value from a field
function getDisplayValue(value) {
  if (value === null || value === undefined || value === '') return '';
  if (typeof value === 'object') {
    if (typeof value.en === 'string') return value.en;
    const firstString = Object.values(value).find(v => typeof v === 'string');
    return firstString || '';
  }
  return value;
}

// Fields to skip from display
const SKIP_KEYS = ['animal_id', 'animal_name', 'animal_type', 'animal_age_1', 'animal_visual_id', 'animal_document_id', 'document_information', 'client_document_information'];
const RELEVANT_KEYS = [
  { key: 'animal_breed_1', label: 'Breed' },
  { key: 'ear_tag_1', label: 'Ear Tag' },
  { key: 'subscription_plan_1', label: 'Plan' },
  { key: 'health_score', label: 'Health' },
];

const getS3ImageUrl = async (animal_document_id, token) => {
  try {
    const response = await fetch(`/api/document?document_id=${animal_document_id}`, {
      headers: { 'token': token }
    });
    if (!response.ok) return null;
    const data = await response.json();
    // Try to find the image URL in the response
    if (data?.data?.url) return data.data.url;
    if (data?.data?.doc_key) return data.data.doc_key;
    return null;
  } catch {
    return null;
  }
};

const CATTLE_TYPES = [
  { value: 'cow', label: 'Cow' },
  { value: 'buffalo', label: 'Buffalo' },
];
const BREEDS = [
  { value: 'HF', label: 'HF' },
  { value: 'Jersey', label: 'Jersey' },
  { value: 'Other', label: 'Other' },
];
const CALVINGS = [1,2,3,4,5,6,7];
const BCS = [
  { value: '1.0', label: '1.0 - Emaciated' },
  { value: '1.5', label: '1.5 - Very Thin' },
  { value: '2.0', label: '2.0 - Thin' },
  { value: '2.5', label: '2.5 - Slightly Thin' },
  { value: '3.0', label: '3.0 - Good' },
  { value: '3.5', label: '3.5 - Above Average' },
  { value: '4.0', label: '4.0 - Fat' },
  { value: '4.5', label: '4.5 - Very Fat' },
  { value: '5.0', label: '5.0 - Obese' },
];

function getInitialEditable(animal) {
  let breed = animal.animal_breed_1;
  if (breed !== 'HF' && breed !== 'Jersey') {
    breed = 'HF';
  }
  return {
    cattleType: animal.animal_type || 'cow',
    breed,
    calvingNumber: animal.calving_number || 1,
    age: animal.animal_age_1 || '',
    dataCollectionDate: '',
    monthsSinceCalving: animal.months_since_calving || '',
    monthsSincePregnancy: animal.months_since_pregnancy || '',
    bodyConditionScore: animal.body_condition_score || '3.0',
    milkProduction: animal.milk_production || '',
    milkProductionDate: '',
  };
}

const month_name = {
  1: 'January', 2: 'February', 3: 'March', 4: 'April', 5: 'May', 6: 'June',
  7: 'July', 8: 'August', 9: 'September', 10: 'October', 11: 'November', 12: 'December'
};

const FarmerAnimals = () => {
  const { farmerId } = useParams();
  const location = useLocation();
  const farmer = location.state?.farmer;
  const userToken = useSelector((state) => state.user.userToken);
  const [animals, setAnimals] = useState([]);
  const [selected, setSelected] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [animalImages, setAnimalImages] = useState({}); // animal_id -> image url
  const [editable, setEditable] = useState({}); // animal_id -> editable fields
  const [collapsed, setCollapsed] = useState({}); // animal_id -> bool
  const [allCollapsed, setAllCollapsed] = useState(false);
  const [chartData, setChartData] = useState(null);
  const [chartOptions, setChartOptions] = useState(null);

  useEffect(() => {
    const fetchAnimals = async () => {
      if (!userToken || !farmerId) return;
      setLoading(true);
      setError(null);
      try {
        const animalsData = await fetchAnimalsForFarmer(userToken, farmerId);
        setAnimals(animalsData);
      } catch (err) {
        setError('Failed to fetch animals.');
        setAnimals([]);
      } finally {
        setLoading(false);
      }
    };
    fetchAnimals();
  }, [userToken, farmerId]);

  // Fetch S3 images for animals with animal_document_id
  useEffect(() => {
    const fetchImages = async () => {
      if (!userToken) return;
      const promises = animals.map(async animal => {
        if (animal.animal_document_id) {
          const url = await getS3ImageUrl(animal.animal_document_id, userToken.accessToken);
          return { animal_id: animal.animal_id, url };
        }
        return null;
      });
      const results = await Promise.all(promises);
      const imageMap = {};
      results.forEach(res => {
        if (res && res.url) imageMap[res.animal_id] = res.url;
      });
      setAnimalImages(imageMap);
    };
    if (animals.length > 0) fetchImages();
  }, [animals, userToken]);

  useEffect(() => {
    if (animals.length > 0) {
      const initial = {};
      animals.forEach(animal => {
        initial[animal.animal_id] = getInitialEditable(animal);
      });
      setEditable(initial);
    }
  }, [animals]);

  const handleSelect = (animalId) => {
    setSelected(prev => prev.includes(animalId) ? prev.filter(id => id !== animalId) : [...prev, animalId]);
  };

  const handleEditChange = (animalId, field, value) => {
    setEditable(prev => ({
      ...prev,
      [animalId]: {
        ...prev[animalId],
        [field]: value,
      },
    }));
  };

  // Collapse/Expand all handler
  const handleCollapseAll = () => {
    const newState = !allCollapsed;
    const newCollapsed = {};
    animals.forEach(animal => {
      newCollapsed[animal.animal_id] = newState;
    });
    setCollapsed(newCollapsed);
    setAllCollapsed(newState);
  };
  // Collapse/Expand individual card
  const handleCollapseCard = (animalId) => {
    setCollapsed(prev => ({ ...prev, [animalId]: !prev[animalId] }));
  };

  const selectedAnimals = animals.filter(a => selected.includes(a.animal_id));

  // Add a function to generate chart data from selected animals
  const handleGenerateChart = () => {
    const selectedAnimals = animals.filter(a => selected.includes(a.animal_id));
    if (selectedAnimals.length === 0) {
      setChartData(null);
      setChartOptions(null);
      return;
    }
    try {
      const dataArray = selectedAnimals.map((animal, idx) => {
        const edit = editable[animal.animal_id] || {};
        let breed = edit.breed;
        if (typeof breed === 'object' && breed !== null && 'value' in breed) {
          breed = breed.value;
        }
        return {
          id: idx + 1,
          month_since_pregnancy: parseInt(edit.monthsSincePregnancy || 0),
          month_since_calving: parseInt(edit.monthsSinceCalving || 0),
          avg_lpd_of_month: parseFloat(edit.milkProduction || 0),
          date_of_avg_lpd: edit.milkProductionDate ? new Date(edit.milkProductionDate) : new Date(),
          data_collection_date: edit.dataCollectionDate ? new Date(edit.dataCollectionDate) : new Date(),
          bcs: parseInt(edit.bodyConditionScore || 3),
          breed: breed || 'HF',
          calvings: parseInt(edit.calvingNumber || 1),
          age: parseInt(edit.age || 3)
        };
      });

      // Get plot data for each animal
      const allPlotData = dataArray.map((data, index) => {
        const plotData = getPlotPoints(data);
        return {
          ...plotData,
          cattleId: data.id
        };
      });
      // Find min/max years and months for x-axis
      let minStartYear0 = Math.min(...allPlotData.map(plot => plot.start_year)) || new Date().getFullYear();
      let maxStartYear0 = Math.max(...allPlotData.map(plot => plot.start_year)) || new Date().getFullYear();
      let minStartYearMonths = 0;
      let maxStartYearMonths = 0;
      allPlotData.forEach(plot => {
        if (plot.start_year === minStartYear0) {
          minStartYearMonths = Math.min(...plot.x);
        }
        if (plot.start_year === maxStartYear0) {
          maxStartYearMonths = Math.max(...plot.x);
        }
      });
      const month_labels = Array.from({length: 33}, (_, i) => {
        const monthNum = minStartYearMonths + i;
        const monthIdx = (monthNum % 12) === 0 ? 12 : monthNum % 12;
        return {
          key: `${monthIdx % 12 === 0 ? minStartYear0++ : minStartYear0}-${monthIdx}-${month_name[monthIdx]}`,
          name: month_name[monthIdx],
          month: monthNum
        };
      });
      
      const lineColors = ['#3b82f6','#ef4444','#22c55e','#f97316','#8b5cf6','#06b6d4','#ec4899','#f59e0b'];

      // Year backgrounds
      const monthsPerYear = 12;
      let minStartYear = Math.min(...allPlotData.map(plot => plot.start_year)) || new Date().getFullYear();
      const yearBackgrounds = {};
      const totalYears = Math.ceil(month_labels.length / monthsPerYear);

      for (let i = 0; i < totalYears; i++) {
        const year = minStartYear + i;
        yearBackgrounds[`year${year}`] = {
          type: 'box',
          xMin: i * monthsPerYear,
          xMax: Math.min((i + 1) * monthsPerYear - 1, month_labels.length - 1),
          yMin: 'min',
          yMax: 'max',
          backgroundColor: 'rgba(0,0,0,0)',
          borderWidth: 0,
          drawTime: 'beforeDatasetsDraw',
          label: {
            display: true,
            content: year.toString(),
            position: {x: 'start', y: 'start'},
            padding: {top: 4, left: 8},
            color: 'rgba(107, 114, 128, 0.8)',
            font: { size: 14, weight: '500', family: 'system-ui' }
          }
        };
      }
      const annotations = {};
      month_labels.forEach((label, index) => {
        allPlotData.forEach((plotData, cattleIndex) => {
          const value = plotData.map[label.key];
          if (typeof value === 'object' && value.message && value.message !== 'Production Data') {
            annotations[`line${index}-${cattleIndex}`] = {
              type: 'line',
              xMin: index,
              xMax: index,
              borderColor: 'rgba(0,0,0,0)',
              borderWidth: 1,
              borderDash: [5, 5],
              borderDashOffset: cattleIndex * 2,
            };
          }
        });
        const month = label.month;
        if (month % 12 === 1 && index > 0) {
          annotations[`yearTransition${index}`] = {
            type: 'line',
            xMin: index,
            xMax: index,
            borderColor: 'rgba(75, 85, 99, 1)',
            borderWidth: 2,
          };
        }
      });
      // Chart.js data and options
      setChartData({
        labels: month_labels.map(label => label.name),
        datasets: allPlotData.map((plotData, index) => ({
          label: `Cattle #${plotData.cattleId}`,
          data: month_labels.map(label => {
            const value = plotData.map[label.key];
            return typeof value === 'object' ? value.lpd : value;
          }),
          borderColor: lineColors[index % lineColors.length],
          backgroundColor: 'transparent',
          borderWidth: 1,
          fill: false,
          tension: 0.4,
          pointRadius: month_labels.map(label => {
            const value = plotData.map[label.key];
            return value ? value.pointRadius : undefined;
          }),
          pointHoverRadius: month_labels.map(() => 8),
          pointStyle: month_labels.map(label => {
            const value = plotData.map[label.key];
            return value ? value.pointStyle : undefined;
          }),
          pointBackgroundColor: month_labels.map(label => {
            const value = plotData.map[label.key];
            return value ? value.color : undefined;
          }),
          pointBorderColor: 'white',
          pointBorderWidth: 1,
        }))
      });
      setChartOptions({
        responsive: true,
        maintainAspectRatio: false,
        aspectRatio: 1,
        plugins: {
          annotation: {
            common: { drawTime: 'beforeDraw' },
            annotations: { ...yearBackgrounds, ...annotations }
          },
          title: {
            display: true,
            text: `Milk Production Forecast ${new Date().getFullYear()}`,
            font: { size: 18, weight: '500', family: 'system-ui' },
            padding: 20
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                const lpd = context.parsed.y;
                if (lpd === null || lpd === undefined) return null;
                const datasetLabel = context.dataset.label;
                const plotData = allPlotData[context.datasetIndex];
                const label = month_labels[context.dataIndex];
                const value = plotData.map[label.key];
                const message = typeof value === 'object' ? value.message : 'Production Data';
                return [`${datasetLabel}: ${lpd.toFixed(2)} LPD`, `Status: ${message}`];
              }
            }
          }
        },
        scales: {
          x: {
            title: {
              display: true,
              text: 'Month',
              font: { size: 14, weight: '500', family: 'system-ui' },
              padding: {top: 15}
            },
            grid: {
              color: 'rgba(226, 232, 240, 0.6)',
              lineWidth: 1,
              drawBorder: true
            },
            ticks: {
              padding: 10,
              font: { size: 12, family: 'system-ui' }
            },
            offset: true,
          },
          y: {
            title: {
              display: true,
              text: 'Liters Per Day (LPD)',
              font: { size: 14, weight: '500', family: 'system-ui' },
              padding: {right: 15}
            },
            grid: {
              color: 'rgba(226, 232, 240, 0.6)',
              lineWidth: 1,
              drawBorder: false,
              drawTicks: false
            },
            ticks: {
              padding: 10,
              font: { size: 12, family: 'system-ui' }
            },
            beginAtZero: true,
            suggestedMax: Math.ceil(Math.max(
              ...allPlotData.flatMap(plot => 
                Object.values(plot.map)
                  .map(v => typeof v === 'object' ? v.lpd : v)
                  .filter(v => typeof v === 'number')
              )
            ) * 1.1)
          }
        },
        layout: {
          padding: { top: 20, right: 20, bottom: 20, left: 20 }
        }
      });
    } catch (err) {
      setChartData(null);
      setChartOptions(null);
      alert(err.message || 'Error generating chart');
    }
  };

  return (
    <div style={{ width: '100%', minHeight: '100vh', background: '#f5f7fa', padding: '32px 32px 32px 32px', boxSizing: 'border-box', fontFamily: 'Inter, Arial, sans-serif' }}>
      <style>{`
        @media (max-width: 1300px) {
          .animal-cards-wrap { justify-content: flex-start !important; }
        }
        @media (max-width: 900px) {
          .animal-cards-wrap { justify-content: center !important; }
        }
        @media (max-width: 700px) {
          .animal-cards-wrap { flex-direction: column !important; align-items: center !important; }
        }
        .animal-cards-wrap::-webkit-scrollbar {
          width: 8px;
          background: #f3f4f6;
        }
        .animal-cards-wrap::-webkit-scrollbar-thumb {
          background: #e3e6ea;
          border-radius: 6px;
        }
        @media (max-width: 900px) {
          .animal-cards-wrap {
            width: 100% !important;
            min-width: 0 !important;
            max-width: 100% !important;
            padding: 12px !important;
          }
          [style*='flex-direction: row'] {
            flex-direction: column !important;
          }
          [style*='padding: 18px 0 18px 18px'] {
            padding: 0 !important;
          }
          .button-row {
            flex-direction: column !important;
            gap: 10px !important;
            align-items: stretch !important;
          }
        }
      `}</style>
      <div style={{ width: '100%' }}>
        {farmer && (
          <div style={{
            background: 'linear-gradient(90deg, #fafdff 70%, #e3f0ff 100%)',
            border: 'none',
            borderLeft: '6px solid #3b82f6',
            borderRadius: 14,
            padding: '18px 28px',
            marginBottom: 28,
            fontSize: 16,
            color: '#23272f',
            boxShadow: '0 4px 18px rgba(60,72,88,0.10)',
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            gap: 28,
            flexWrap: 'wrap',
            minHeight: 56,
            lineHeight: 1.4,
            position: 'relative',
            overflow: 'hidden',
            fontFamily: 'Inter, Arial, sans-serif',
            transition: 'box-shadow 0.18s, border 0.18s, transform 0.12s',
          }}>
            <div style={{display:'flex', alignItems:'center', gap:10, fontWeight:700, fontSize:20, color:'#1a2233'}}>
              <span style={{display:'inline-flex', alignItems:'center', justifyContent:'center', width:28, height:28, background:'#3b82f6', color:'#fff', borderRadius:'50%', fontSize:18, marginRight:6}}>
                <svg width="18" height="18" fill="none" viewBox="0 0 24 24"><path fill="currentColor" d="M12 12c2.7 0 8 1.34 8 4v2H4v-2c0-2.66 5.3-4 8-4Zm0-2a4 4 0 1 1 0-8 4 4 0 0 1 0 8Z"/></svg>
              </span>
              {farmer.customer_name || '-'}
            </div>
            <div style={{display:'flex', alignItems:'center', gap:7, fontSize:16, color:'#3b82f6'}}>
              <span style={{display:'inline-flex', alignItems:'center'}}>
                <svg width="18" height="18" fill="none" viewBox="0 0 24 24"><path fill="currentColor" d="M6.62 10.79a15.053 15.053 0 0 0 6.59 6.59l2.2-2.2a1 1 0 0 1 1.01-.24c1.12.37 2.33.57 3.58.57a1 1 0 0 1 1 1V20a1 1 0 0 1-1 1C10.07 21 3 13.93 3 5a1 1 0 0 1 1-1h3.5a1 1 0 0 1 1 1c0 1.25.2 2.46.57 3.58a1 1 0 0 1-.24 1.01l-2.2 2.2Z"/></svg>
              </span>
              {farmer.mobile_number || '-'}
            </div>
            <div style={{display:'flex', alignItems:'center', gap:7, fontSize:16, color:'#1a2233'}}>
              <span style={{display:'inline-flex', alignItems:'center'}}>
                <svg width="18" height="18" fill="none" viewBox="0 0 24 24"><path fill="currentColor" d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7Zm0 9.5A2.5 2.5 0 1 1 12 6a2.5 2.5 0 0 1 0 5.5Z"/></svg>
              </span>
              {farmer.village || '-'}
            </div>
            <div style={{display:'flex', alignItems:'center', gap:7, fontSize:16, color:'#1a2233'}}>
              <span style={{display:'inline-flex', alignItems:'center'}}>
                <svg width="18" height="18" fill="none" viewBox="0 0 24 24"><path fill="currentColor" d="M19.07 4.93A10 10 0 0 0 4.93 19.07 10 10 0 1 0 19.07 4.93ZM12 20a8 8 0 1 1 0-16 8 8 0 0 1 0 16Z"/></svg>
              </span>
              {farmer.taluk || '-'}
            </div>
            <div style={{display:'flex', alignItems:'center', gap:7, fontSize:16, color:'#1a2233'}}>
              <span style={{display:'inline-flex', alignItems:'center'}}>
                <svg width="18" height="18" fill="none" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" stroke="#3b82f6" strokeWidth="2" fill="none"/><text x="12" y="16" textAnchor="middle" fontSize="10" fill="#3b82f6">ID</text></svg>
              </span>
              {farmer.customer_visual_id || '-'}
            </div>
          </div>
        )}
        <div style={{height:1, background:'#e3e6ea', margin:'24px 0 32px 0', borderRadius:2}} />
        <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: 16, marginBottom: 24, flexWrap: 'wrap' }}>
          <button
            onClick={handleCollapseAll}
            style={{ padding: '10px 22px', fontSize: 16, borderRadius: 8, border: '1px solid #e3e6ea', background: '#f6f7fa', fontWeight: 700, cursor: 'pointer', color: '#23272f', boxShadow:'0 1px 4px rgba(60,72,88,0.04)', transition:'background 0.2s' }}
          >
            {allCollapsed ? 'Expand All' : 'Collapse All'}
          </button>
          <button
            onClick={handleGenerateChart}
            style={{padding:'10px 22px',fontWeight:700,fontSize:16,borderRadius:8,background:'#3b82f6',color:'#fff',border:'none',cursor:'pointer',boxShadow:'0 1px 4px rgba(60,72,88,0.04)', transition:'background 0.2s'}}>
            Generate Milk Production Chart
          </button>
        </div>
        <div style={{fontWeight:700, fontSize:20, marginBottom:18, color:'#1a2233', letterSpacing:'-0.5px'}}>Animal List</div>
        {loading ? (
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 160 }}>
            <Spin size="large" />
          </div>
        ) : error ? (
          <Alert type="error" message={error} showIcon style={{ marginBottom: 18 }} />
        ) : animals.length === 0 ? (
          <div style={{ color: '#888', fontSize: 16, marginTop: 32 }}>No animals found for this farmer.</div>
        ) : (
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'flex-start',
              gap: 32,
              width: '100%',
              boxSizing: 'border-box',
              minHeight: 600,
              flexWrap: 'wrap',
            }}
          >
            {/* Left: Animal Cards */}
            <div className="animal-cards-wrap" style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'stretch',
              gap: 24,
              width: 400,
              minWidth: 320,
              maxWidth: 480,
              padding: 18,
              border: '1px solid #e3e6ea',
              borderRadius: 12,
              background: '#fafdff',
              boxShadow: '0 2px 8px rgba(60,72,88,0.04)',
              maxHeight: '80vh',
              overflowY: 'auto',
              margin: 0,
            }}>
              {animals.map(animal => {
                // Priority: S3 image (animal_document_id) > document_information.url > placeholder
                let imgUrl = animalImages[animal.animal_id];
                if (!imgUrl && animal.document_information?.url) {
                  imgUrl = `https://ahs3.krushal.in/${animal.document_information.url}`;
                }
                if (!imgUrl) imgUrl = placeholderImg;
                return (
                  <div
                    key={animal.animal_id}
                    style={{
                      ...cardStyle,
                      ...(selected.includes(animal.animal_id) ? selectedCardStyle : {}),
                      minHeight: collapsed[animal.animal_id] ? 100 : 630,
                      width: '100%',
                      margin: 0,
                      marginBottom: 18,
                      boxShadow: selected.includes(animal.animal_id)
                        ? '0 4px 18px rgba(24,144,255,0.13)'
                        : '0 2px 8px rgba(60,72,88,0.08)',
                      transform: selected.includes(animal.animal_id)
                        ? 'scale(1.018)'
                        : 'scale(1)',
                      transition: 'box-shadow 0.18s, border 0.18s, transform 0.12s',
                      border: selected.includes(animal.animal_id) ? '2px solid #1890ff' : '1px solid #e3e6ea',
                      background: selected.includes(animal.animal_id) ? '#f6fbff' : '#fff',
                      cursor: 'pointer',
                    }}
                    onMouseDown={e => {
                      if (e.target.closest('.collapse-btn')) return;
                      // Start long press timer
                      e.currentTarget.longPressTimer = setTimeout(() => {
                        handleSelect(animal.animal_id);
                      }, 500);
                    }}
                    onMouseUp={e => {
                      clearTimeout(e.currentTarget.longPressTimer);
                    }}
                    onMouseLeave={e => {
                      clearTimeout(e.currentTarget.longPressTimer);
                    }}
                  >
                    <button
                      className="collapse-btn"
                      onClick={e => { e.stopPropagation(); handleCollapseCard(animal.animal_id); }}
                      style={{ position: 'absolute', top: 8, right: 48, zIndex: 3, background: '#e3e6ea', border: 'none', borderRadius: 4, padding: '2px 10px', fontWeight: 700, fontSize: 16, cursor: 'pointer', color:'#23272f', boxShadow:'0 1px 4px rgba(60,72,88,0.08)', marginLeft: 8 }}
                    >
                      {collapsed[animal.animal_id] ? '+' : '−'}
                    </button>
                    <Checkbox
                      checked={selected.includes(animal.animal_id)}
                      onChange={() => handleSelect(animal.animal_id)}
                      style={checkboxStyle}
                      onClick={e => e.stopPropagation()}
                    />
                    <div style={{...contentStyle, fontSize:16}}>
                      <div style={{...nameStyle, fontSize:20, marginBottom:4}}>
                        {getDisplayValue(animal.animal_name) || getDisplayValue(animal.animal_visual_id) || animal.animal_id}
                      </div>
                      <div style={{...typeStyle, fontSize:16}}>{getDisplayValue(animal.animal_type) || '-'}</div>
                      <div style={{...ageStyle, fontSize:15}}>Age: {getDisplayValue(animal.animal_age_1) || '-'}</div>
                      {/* Collapsible content */}
                      {!collapsed[animal.animal_id] && (
                        <>
                          <div style={{ margin: '2px 0 0 0' }}>
                            {RELEVANT_KEYS.map(({ key, label }) => {
                              const value = getDisplayValue(animal[key]);
                              if (!value) return null;
                              return (
                                <div key={key} style={{...detailRowStyle, fontSize:15}}>
                                  <span style={{...detailLabelStyle, fontSize:15}}>{label}:</span>
                                  <span style={{...detailValueStyle, fontSize:15}}>{value}</span>
                                </div>
                              );
                            })}
                          </div>
                          {/* Editable fields for milk projection */}
                          <div style={{ marginTop: 10, borderTop: '1px solid #eee', paddingTop: 10 }}>
                            <div style={{ marginBottom: 8, display:'flex', alignItems:'center' }}>
                              <label style={{...detailLabelStyle, fontWeight:600, fontSize:15, marginRight:8}}>Cattle Type:</label>
                              <select value={editable[animal.animal_id]?.cattleType || ''} onChange={e => handleEditChange(animal.animal_id, 'cattleType', e.target.value)} style={{ ...detailValueStyle, minWidth: 70, fontSize: 15, padding: '6px 10px', borderRadius:6, border:'1px solid #e3e6ea', background:'#fafdff' }}>
                                {CATTLE_TYPES.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
                              </select>
                            </div>
                            <div style={{ marginBottom: 8, display:'flex', alignItems:'center' }}>
                              <label style={{...detailLabelStyle, fontWeight:600, fontSize:15, marginRight:8}}>Breed:</label>
                              <select value={editable[animal.animal_id]?.breed || 'HF'} onChange={e => handleEditChange(animal.animal_id, 'breed', e.target.value)} style={{ ...detailValueStyle, minWidth: 70, fontSize: 15, padding: '6px 10px', borderRadius:6, border:'1px solid #e3e6ea', background:'#fafdff' }}>
                                {BREEDS.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
                              </select>
                            </div>
                            <div style={{ marginBottom: 8, display:'flex', alignItems:'center' }}>
                              <label style={{...detailLabelStyle, fontWeight:600, fontSize:15, marginRight:8}}>Calving #:</label>
                              <select value={editable[animal.animal_id]?.calvingNumber || 1} onChange={e => handleEditChange(animal.animal_id, 'calvingNumber', e.target.value)} style={{ ...detailValueStyle, minWidth: 40, fontSize: 15, padding: '6px 10px', borderRadius:6, border:'1px solid #e3e6ea', background:'#fafdff' }}>
                                {CALVINGS.map(num => <option key={num} value={num}>{num}</option>)}
                              </select>
                            </div>
                            <div style={{ marginBottom: 8, display:'flex', alignItems:'center' }}>
                              <label style={{...detailLabelStyle, fontWeight:600, fontSize:15, marginRight:8}}>Age:</label>
                              <input type="number" min={2} max={15} value={editable[animal.animal_id]?.age || ''} onChange={e => handleEditChange(animal.animal_id, 'age', e.target.value)} style={{ ...detailValueStyle, minWidth: 40, fontSize: 15, padding: '6px 10px', borderRadius:6, border:'1px solid #e3e6ea', background:'#fafdff' }} />
                            </div>
                            <div style={{ marginBottom: 8, display:'flex', alignItems:'center' }}>
                              <label style={{...detailLabelStyle, fontWeight:600, fontSize:15, marginRight:8}}>Data Date:</label>
                              <input type="datetime-local" value={editable[animal.animal_id]?.dataCollectionDate || ''} onChange={e => handleEditChange(animal.animal_id, 'dataCollectionDate', e.target.value)} style={{ ...detailValueStyle, minWidth: 120, fontSize: 15, padding: '6px 10px', borderRadius:6, border:'1px solid #e3e6ea', background:'#fafdff' }} />
                            </div>
                            <div style={{ marginBottom: 8, display:'flex', alignItems:'center' }}>
                              <label style={{...detailLabelStyle, fontWeight:600, fontSize:15, marginRight:8}}>Months Since Calving:</label>
                              <input type="number" min={0} max={120} value={editable[animal.animal_id]?.monthsSinceCalving || ''} onChange={e => handleEditChange(animal.animal_id, 'monthsSinceCalving', e.target.value)} style={{ ...detailValueStyle, minWidth: 40, fontSize: 15, padding: '6px 10px', borderRadius:6, border:'1px solid #e3e6ea', background:'#fafdff' }} />
                            </div>
                            <div style={{ marginBottom: 8, display:'flex', alignItems:'center' }}>
                              <label style={{...detailLabelStyle, fontWeight:600, fontSize:15, marginRight:8}}>Months Since Pregnancy:</label>
                              <input type="number" min={0} max={120} value={editable[animal.animal_id]?.monthsSincePregnancy || ''} onChange={e => handleEditChange(animal.animal_id, 'monthsSincePregnancy', e.target.value)} style={{ ...detailValueStyle, minWidth: 40, fontSize: 15, padding: '6px 10px', borderRadius:6, border:'1px solid #e3e6ea', background:'#fafdff' }} />
                            </div>
                            <div style={{ marginBottom: 8, display:'flex', alignItems:'center' }}>
                              <label style={{...detailLabelStyle, fontWeight:600, fontSize:15, marginRight:8}}>Body Condition Score:</label>
                              <select value={editable[animal.animal_id]?.bodyConditionScore || '3.0'} onChange={e => handleEditChange(animal.animal_id, 'bodyConditionScore', e.target.value)} style={{ ...detailValueStyle, minWidth: 60, fontSize: 15, padding: '6px 10px', borderRadius:6, border:'1px solid #e3e6ea', background:'#fafdff' }}>
                                {BCS.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
                              </select>
                            </div>
                            <div style={{ marginBottom: 8, display:'flex', alignItems:'center' }}>
                              <label style={{...detailLabelStyle, fontWeight:600, fontSize:15, marginRight:8}}>Avg. Daily Milk (L):</label>
                              <input type="number" min={0} step={0.1} value={editable[animal.animal_id]?.milkProduction || ''} onChange={e => handleEditChange(animal.animal_id, 'milkProduction', e.target.value)} style={{ ...detailValueStyle, minWidth: 40, fontSize: 15, padding: '6px 10px', borderRadius:6, border:'1px solid #e3e6ea', background:'#fafdff' }} />
                            </div>
                            <div style={{ marginBottom: 0, display:'flex', alignItems:'center' }}>
                              <label style={{...detailLabelStyle, fontWeight:600, fontSize:15, marginRight:8}}>Milk Prod. Date:</label>
                              <input type="date" value={editable[animal.animal_id]?.milkProductionDate || ''} onChange={e => handleEditChange(animal.animal_id, 'milkProductionDate', e.target.value)} style={{ ...detailValueStyle, minWidth: 90, fontSize: 15, padding: '6px 10px', borderRadius:6, border:'1px solid #e3e6ea', background:'#fafdff' }} />
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
            {/* Right: Chart */}
            <div style={{ flex: 1, minWidth: 0, display: 'flex', flexDirection: 'column', alignItems: 'stretch', justifyContent: 'flex-start', padding: '18px 0 18px 18px' }}>
              {chartData && chartOptions && (
                <div style={{width:'100%',minHeight:500,background:'#fff',borderRadius:12,boxShadow:'0 2px 12px rgba(60,72,88,0.08)',padding:24,overflow:'auto'}}>
                  <Line data={chartData} options={chartOptions} height={500} />
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FarmerAnimals; 
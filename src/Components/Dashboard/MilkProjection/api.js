import { getFarmersReport } from '../../../router';

export async function fetchFarmers(userToken) {
  if (!userToken) return [];
  const headers = {
    useCase: 'Get Farmers With Location Recorded Report',
    token: userToken.accessToken,
  };
  const response = await getFarmersReport(headers, {});
  return response?.data?.report || [];
}

export async function fetchAnimalsForFarmer(userToken, farmerId) {
  if (!userToken || !farmerId) return [];
  const url = `https://ahs3.krushal.in/api/animal?customer_id=${farmerId}`;
  const headers = {
    'Content-Type': 'application/json',
    'token': userToken.accessToken,
  };
  const response = await fetch(url, { headers });
  if (!response.ok) return [];
  const data = await response.json();
  return data?.data || [];
} 
export const month_name = {
    1: "January",
    2: "February",      
    3: "March",
    4: "April",
    5: "May",
    6: "June",
    7: "July",
    8: "August",
    9: "September", 
    10: "October",
    11: "November",
    12: "December"
};

export function getMonthsDifference(date1, date2) {
    const d1 = new Date(date1);
    const d2 = new Date(date2);
    const yearsDiff = d1.getFullYear() - d2.getFullYear();
    const monthsDiff = d1.getMonth() - d2.getMonth();
    return yearsDiff * 12 + monthsDiff;
}

export function validateMilkProductionDate(milkProdDate, calvingDate) {
    if (milkProdDate < calvingDate) {
        throw new Error("Milk production date cannot be before calving date");
    }
}

export const bcsData = [
    {breed: "HF", bcs: 2, calvings: 1, reductionFactor: 0.080, aiNeeded: 3, firstAIMonth: 4, firstDryingMonth: 13, intercalvingPeriod: 15},
    {breed: "HF", bcs: 2, calvings: 2, reductionFactor: 0.075, aiNeeded: 3, firstAIMonth: 4, firstDryingMonth: 13, intercalvingPeriod: 15},
    {breed: "HF", bcs: 2, calvings: 3, reductionFactor: 0.075, aiNeeded: 3, firstAIMonth: 4, firstDryingMonth: 13, intercalvingPeriod: 15},
    {breed: "HF", bcs: 2, calvings: 4, reductionFactor: 0.075, aiNeeded: 3, firstAIMonth: 4, firstDryingMonth: 13, intercalvingPeriod: 15},
    {breed: "HF", bcs: 2, calvings: 5, reductionFactor: 0.075, aiNeeded: 3, firstAIMonth: 4, firstDryingMonth: 13, intercalvingPeriod: 15},
    {breed: "HF", bcs: 2, calvings: 6, reductionFactor: 0.075, aiNeeded: 3, firstAIMonth: 4, firstDryingMonth: 13, intercalvingPeriod: 15},
    {breed: "HF", bcs: 2, calvings: 7, reductionFactor: 0.080, aiNeeded: 3, firstAIMonth: 4, firstDryingMonth: 13, intercalvingPeriod: 15},
    {breed: "HF", bcs: 3, calvings: 1, reductionFactor: 0.075, aiNeeded: 1, firstAIMonth: 4, firstDryingMonth: 11, intercalvingPeriod: 13},
    {breed: "HF", bcs: 3, calvings: 2, reductionFactor: 0.070, aiNeeded: 1, firstAIMonth: 4, firstDryingMonth: 11, intercalvingPeriod: 13},
    {breed: "HF", bcs: 3, calvings: 3, reductionFactor: 0.070, aiNeeded: 1, firstAIMonth: 4, firstDryingMonth: 11, intercalvingPeriod: 13},
    {breed: "HF", bcs: 3, calvings: 4, reductionFactor: 0.070, aiNeeded: 1, firstAIMonth: 4, firstDryingMonth: 11, intercalvingPeriod: 13},
    {breed: "HF", bcs: 3, calvings: 5, reductionFactor: 0.070, aiNeeded: 1, firstAIMonth: 4, firstDryingMonth: 11, intercalvingPeriod: 13},
    {breed: "HF", bcs: 3, calvings: 6, reductionFactor: 0.070, aiNeeded: 1, firstAIMonth: 4, firstDryingMonth: 11, intercalvingPeriod: 13},
    {breed: "HF", bcs: 3, calvings: 7, reductionFactor: 0.075, aiNeeded: 1, firstAIMonth: 4, firstDryingMonth: 11, intercalvingPeriod: 13},
    {breed: "HF", bcs: 4, calvings: 1, reductionFactor: 0.070, aiNeeded: 2, firstAIMonth: 4, firstDryingMonth: 12, intercalvingPeriod: 14},
    {breed: "HF", bcs: 4, calvings: 2, reductionFactor: 0.065, aiNeeded: 2, firstAIMonth: 4, firstDryingMonth: 12, intercalvingPeriod: 14},
    {breed: "HF", bcs: 4, calvings: 3, reductionFactor: 0.065, aiNeeded: 2, firstAIMonth: 4, firstDryingMonth: 12, intercalvingPeriod: 14},
    {breed: "HF", bcs: 4, calvings: 4, reductionFactor: 0.065, aiNeeded: 2, firstAIMonth: 4, firstDryingMonth: 12, intercalvingPeriod: 14},
    {breed: "HF", bcs: 4, calvings: 5, reductionFactor: 0.065, aiNeeded: 2, firstAIMonth: 4, firstDryingMonth: 12, intercalvingPeriod: 14},
    {breed: "HF", bcs: 4, calvings: 6, reductionFactor: 0.065, aiNeeded: 2, firstAIMonth: 4, firstDryingMonth: 12, intercalvingPeriod: 14},
    {breed: "HF", bcs: 4, calvings: 7, reductionFactor: 0.070, aiNeeded: 2, firstAIMonth: 4, firstDryingMonth: 12, intercalvingPeriod: 14},
    {breed: "HF", bcs: 5, calvings: 1, reductionFactor: 0.075, aiNeeded: 3, firstAIMonth: 4, firstDryingMonth: 13, intercalvingPeriod: 15},
    {breed: "HF", bcs: 5, calvings: 2, reductionFactor: 0.070, aiNeeded: 3, firstAIMonth: 4, firstDryingMonth: 13, intercalvingPeriod: 15},
    {breed: "HF", bcs: 5, calvings: 3, reductionFactor: 0.070, aiNeeded: 3, firstAIMonth: 4, firstDryingMonth: 13, intercalvingPeriod: 15},
    {breed: "HF", bcs: 5, calvings: 4, reductionFactor: 0.070, aiNeeded: 3, firstAIMonth: 4, firstDryingMonth: 13, intercalvingPeriod: 15},
    {breed: "HF", bcs: 5, calvings: 5, reductionFactor: 0.070, aiNeeded: 3, firstAIMonth: 4, firstDryingMonth: 13, intercalvingPeriod: 15},
    {breed: "HF", bcs: 5, calvings: 6, reductionFactor: 0.070, aiNeeded: 3, firstAIMonth: 4, firstDryingMonth: 13, intercalvingPeriod: 15},
    {breed: "HF", bcs: 5, calvings: 7, reductionFactor: 0.075, aiNeeded: 3, firstAIMonth: 4, firstDryingMonth: 13, intercalvingPeriod: 15},
    {breed: "Jersey", bcs: 2, calvings: 1, reductionFactor: 0.080, aiNeeded: 3, firstAIMonth: 4, firstDryingMonth: 13, intercalvingPeriod: 15},
    {breed: "Jersey", bcs: 2, calvings: 2, reductionFactor: 0.080, aiNeeded: 3, firstAIMonth: 4, firstDryingMonth: 13, intercalvingPeriod: 15},
    {breed: "Jersey", bcs: 2, calvings: 3, reductionFactor: 0.080, aiNeeded: 3, firstAIMonth: 4, firstDryingMonth: 13, intercalvingPeriod: 15},
    {breed: "Jersey", bcs: 2, calvings: 4, reductionFactor: 0.080, aiNeeded: 3, firstAIMonth: 4, firstDryingMonth: 13, intercalvingPeriod: 15},
    {breed: "Jersey", bcs: 2, calvings: 5, reductionFactor: 0.080, aiNeeded: 3, firstAIMonth: 4, firstDryingMonth: 13, intercalvingPeriod: 15},
    {breed: "Jersey", bcs: 2, calvings: 6, reductionFactor: 0.080, aiNeeded: 3, firstAIMonth: 4, firstDryingMonth: 13, intercalvingPeriod: 15},
    {breed: "Jersey", bcs: 2, calvings: 7, reductionFactor: 0.080, aiNeeded: 3, firstAIMonth: 4, firstDryingMonth: 13, intercalvingPeriod: 15},
    {breed: "Jersey", bcs: 3, calvings: 1, reductionFactor: 0.075, aiNeeded: 1, firstAIMonth: 4, firstDryingMonth: 11, intercalvingPeriod: 13},
    {breed: "Jersey", bcs: 3, calvings: 2, reductionFactor: 0.075, aiNeeded: 1, firstAIMonth: 4, firstDryingMonth: 11, intercalvingPeriod: 13},
    {breed: "Jersey", bcs: 3, calvings: 3, reductionFactor: 0.075, aiNeeded: 1, firstAIMonth: 4, firstDryingMonth: 11, intercalvingPeriod: 13},
    {breed: "Jersey", bcs: 3, calvings: 4, reductionFactor: 0.075, aiNeeded: 1, firstAIMonth: 4, firstDryingMonth: 11, intercalvingPeriod: 13},
    {breed: "Jersey", bcs: 3, calvings: 5, reductionFactor: 0.075, aiNeeded: 1, firstAIMonth: 4, firstDryingMonth: 11, intercalvingPeriod: 13},
    {breed: "Jersey", bcs: 3, calvings: 6, reductionFactor: 0.075, aiNeeded: 1, firstAIMonth: 4, firstDryingMonth: 11, intercalvingPeriod: 13},
    {breed: "Jersey", bcs: 3, calvings: 7, reductionFactor: 0.075, aiNeeded: 1, firstAIMonth: 4, firstDryingMonth: 11, intercalvingPeriod: 13},
    {breed: "Jersey", bcs: 4, calvings: 1, reductionFactor: 0.070, aiNeeded: 2, firstAIMonth: 4, firstDryingMonth: 12, intercalvingPeriod: 14},
    {breed: "Jersey", bcs: 4, calvings: 2, reductionFactor: 0.070, aiNeeded: 2, firstAIMonth: 4, firstDryingMonth: 12, intercalvingPeriod: 14},
    {breed: "Jersey", bcs: 4, calvings: 3, reductionFactor: 0.070, aiNeeded: 2, firstAIMonth: 4, firstDryingMonth: 12, intercalvingPeriod: 14},
    {breed: "Jersey", bcs: 4, calvings: 4, reductionFactor: 0.070, aiNeeded: 2, firstAIMonth: 4, firstDryingMonth: 12, intercalvingPeriod: 14},
    {breed: "Jersey", bcs: 4, calvings: 5, reductionFactor: 0.070, aiNeeded: 2, firstAIMonth: 4, firstDryingMonth: 12, intercalvingPeriod: 14},
    {breed: "Jersey", bcs: 4, calvings: 6, reductionFactor: 0.070, aiNeeded: 2, firstAIMonth: 4, firstDryingMonth: 12, intercalvingPeriod: 14},
    {breed: "Jersey", bcs: 4, calvings: 7, reductionFactor: 0.070, aiNeeded: 2, firstAIMonth: 4, firstDryingMonth: 12, intercalvingPeriod: 14},
    {breed: "Jersey", bcs: 5, calvings: 1, reductionFactor: 0.075, aiNeeded: 3, firstAIMonth: 4, firstDryingMonth: 13, intercalvingPeriod: 15},
    {breed: "Jersey", bcs: 5, calvings: 2, reductionFactor: 0.075, aiNeeded: 3, firstAIMonth: 4, firstDryingMonth: 13, intercalvingPeriod: 15},
    {breed: "Jersey", bcs: 5, calvings: 3, reductionFactor: 0.075, aiNeeded: 3, firstAIMonth: 4, firstDryingMonth: 13, intercalvingPeriod: 15},
    {breed: "Jersey", bcs: 5, calvings: 4, reductionFactor: 0.075, aiNeeded: 3, firstAIMonth: 4, firstDryingMonth: 13, intercalvingPeriod: 15},
    {breed: "Jersey", bcs: 5, calvings: 5, reductionFactor: 0.075, aiNeeded: 3, firstAIMonth: 4, firstDryingMonth: 13, intercalvingPeriod: 15},
    {breed: "Jersey", bcs: 5, calvings: 6, reductionFactor: 0.075, aiNeeded: 3, firstAIMonth: 4, firstDryingMonth: 13, intercalvingPeriod: 15},
    {breed: "Jersey", bcs: 5, calvings: 7, reductionFactor: 0.075, aiNeeded: 3, firstAIMonth: 4, firstDryingMonth: 13, intercalvingPeriod: 15}
];

export function getCattleParameters(breed, bcs, calvings) {
    if (!breed || !["HF", "Jersey"].includes(breed)) {
        throw new Error("Invalid breed. Must be 'HF' or 'Jersey'");
    }
    if (!bcs || bcs < 1 || bcs > 5) {
        throw new Error("Invalid BCS. Must be between 1 and 5");
    }
    if (!calvings || calvings > 7 || calvings < 1) {
        throw new Error("Invalid calvings number. Must be between 1 and 7");
    }

    const cattleData = bcsData.find(data => 
        data.breed === breed && 
        data.bcs === bcs && 
        data.calvings === calvings
    );

    if (!cattleData) {
        throw new Error("Could not find matching cattle reduction parameters for breed: " + breed + ", BCS: " + bcs + ", calvings: " + calvings);
    }

    let reductionFactor = cattleData.reductionFactor;

    return {
        ...cattleData,
        reductionFactor
    };
}

export function calculateAvgLPDAfterCalvingMonth(month_of_avg_lpd, avg_lpd_of_month, calving_month, milk_reduction_rate, month_offset) {
    if (month_of_avg_lpd === calving_month) {
        return avg_lpd_of_month;
    }
    return avg_lpd_of_month / (Math.pow(milk_reduction_rate, month_of_avg_lpd - month_offset) * 2);
}

export function calculateAvgLpdOfTheMonth(month, data) {
    const { month_of_calving, pregnancy_try_start_month, ideal_pregnancy_month, avg_lpd_of_calving_month, milk_reduction_rate, month_offset } = data;
    let is_dry_month;
    if (pregnancy_try_start_month) {
        is_dry_month = month >= pregnancy_try_start_month + 8 - 1 && month <= pregnancy_try_start_month + 8;
    }

    switch (true) {
        case month === month_of_calving:
            if (month === pregnancy_try_start_month) {
                return {
                    lpd: avg_lpd_of_calving_month,
                    color: "red",
                    message: "Error! Cow calved and pregnant in same month",
                    pointRadius: 8,
                    pointStyle: 'triangle',
                    borderWidth: 2
                };
            }
            return {
                lpd: avg_lpd_of_calving_month,
                color: "pink",
                pointRadius: 5,
                message: "Calving month"
            };
        case month === pregnancy_try_start_month:
            return {
                lpd: 2 * avg_lpd_of_calving_month * Math.pow(milk_reduction_rate, month - month_offset),
                color: "#2ef9ea",
                pointRadius: 5,
                message: "Pregnancy started"
            };
        case month === ideal_pregnancy_month:
            return {
                lpd: 2 * avg_lpd_of_calving_month * Math.pow(milk_reduction_rate, month - month_offset),
                color: "blue",
                pointRadius: 5,
                message: "Ideal pregnancy month"
            };
        case month < month_of_calving:
            return {
                lpd: 0,
                color: "grey",
                pointRadius: 1,
                message: "Before calving"
            };
        case is_dry_month:
            return {
                lpd: 0,
                color: "red",
                pointRadius: 5,
                message: "Dry period"
            };
        default:
            return {
                month: month_name[month % 12 === 0 ? 12 : month % 12],
                lpd: 2 * avg_lpd_of_calving_month * Math.pow(milk_reduction_rate, month - month_offset),
                color: "green",
                pointRadius: 3,
                message: "Lactation"
            };
    }
}

export function getPlotPoints(data) {
    console.log("getPlotPoints",data)
    const { reductionFactor, firstAIMonth, firstDryingMonth } = getCattleParameters(
        data.breed || 'HF',
        data.bcs || 3,
        data.calvings || 1
    );

    let { data_collection_date, month_since_pregnancy, month_since_calving, date_of_avg_lpd, avg_lpd_of_month } = data;
    let date_of_pregnancy = new Date(data_collection_date);
    let date_of_calving = new Date(data_collection_date);
    date_of_pregnancy.setMonth(date_of_pregnancy.getMonth() - month_since_pregnancy);
    date_of_calving.setMonth(date_of_calving.getMonth() - month_since_calving);
    let year = date_of_calving.getFullYear();
    const diff_in_pregnancy_and_calving_months = getMonthsDifference(date_of_pregnancy, date_of_calving);

    if (!date_of_avg_lpd) {
        throw new Error("Milk production date is required");
    }
    validateMilkProductionDate(date_of_avg_lpd, date_of_calving);
    const month_of_avg_lpd_from_calving = getMonthsDifference(date_of_avg_lpd, date_of_calving);
    if (month_of_avg_lpd_from_calving < 0) {
        throw new Error("Average milk production month cannot be before calving month");
    }
    if (diff_in_pregnancy_and_calving_months < 0 || diff_in_pregnancy_and_calving_months < 4) {
        throw new Error("Pregnancy month cannot be before calving month");
    }
    const month_of_calving = date_of_calving.getMonth() + 1;
    const relative_month_of_pregnancy = month_of_calving + diff_in_pregnancy_and_calving_months;
    const relative_month_of_avg_lpd = month_of_calving + month_of_avg_lpd_from_calving;
    const milk_reduction_rate = 1 - reductionFactor;
    let month_offset = month_of_calving + 1;
    let avg_lpd_of_calving_month = calculateAvgLPDAfterCalvingMonth(
        relative_month_of_avg_lpd,
        avg_lpd_of_month,
        month_of_calving,
        milk_reduction_rate,
        month_offset
    );
    let month = month_of_calving;
    let plot = {
        start_year: date_of_calving.getFullYear(),
        x: [],
        y: [],
        map: {}
    };
    for (let cycle = 0; cycle < 1; cycle++) {
        let m_pregnancy = relative_month_of_pregnancy;
        let m_calving = month_of_calving;
        let m_avg_lpd = relative_month_of_avg_lpd;
        let y_value = undefined;
        let next_calving_month = relative_month_of_pregnancy + 9;
        plot = {
            start_year: date_of_calving.getFullYear(),
            x: [],
            y: [],
            map: {},
            data_collection_month:getMonthsDifference(data_collection_date, date_of_calving )
        };
        for (let m = 1; m <= 28; m++) {
            if (month === next_calving_month) {
                m_calving = next_calving_month;
                m_pregnancy = m_calving + firstAIMonth;
                m_avg_lpd = relative_month_of_avg_lpd;
                next_calving_month = m_calving + firstAIMonth + 9;
                month_offset = m_calving + 1;
            }
            y_value = calculateAvgLpdOfTheMonth(month, {
                month_of_calving: m_calving,
                month_of_pregnancy: m_pregnancy,
                month_of_avg_lpd: m_avg_lpd,
                avg_lpd_of_calving_month: avg_lpd_of_calving_month,
                pregnancy_try_start_month: m_pregnancy,
                milk_reduction_rate,
                ideal_pregnancy_month: m_calving + 4,
                month_offset: month_offset
            });
            plot.x.push(month);
            plot.y.push(y_value);
            plot.map[`${year}-${month % 12 === 0 ? 12 : month % 12}-${month_name[month % 12 === 0 ? 12 : month % 12]}`] = y_value;
            if (month % 12 === 0) {
                year++;
            }
            month++;
        }
    }
    return plot;
}
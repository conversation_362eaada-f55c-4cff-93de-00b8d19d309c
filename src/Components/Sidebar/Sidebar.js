import React, { useEffect, useState } from "react";
import {
  CoffeeOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  TeamOutlined,
  AlignCenterOutlined,
  ContactsOutlined,
  OrderedListOutlined,
  NodeExpandOutlined,
  EnvironmentOutlined,
  BarsOutlined,
  AimOutlined,
  ApartmentOutlined,
  CustomerServiceOutlined,
  LogoutOutlined,
  UserAddOutlined,
  ClusterOutlined,
  ScheduleOutlined,
} from "@ant-design/icons";
import { Link } from "react-router-dom";
import { Layout, Menu, Button, theme } from "antd";
import "./Sidebar.css";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useLocation } from "react-router-dom";
import { clearUserToken } from "../user/action";
import { BASE_URL } from "../../Services/api.service";
import krushalLogo from "../Dashboard/ServiceRequests/krushal.png";

import { auth } from "../../Config/firebase.config";

const { Head<PERSON>, Sider } = Layout;
const Sidebar = (props) => {
  const [collapsed, setCollapsed] = useState(false);
  const [dropdownVisible, setDropdownVisible] = useState(false);

  const [profileImage, setProfileImage] = useState();

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const userData = useSelector((state) => state.user.userData);

  const handleDropdownClick = (e) => {
    e.preventDefault();
    setDropdownVisible(!dropdownVisible);
  };

  const {
    token: { colorBgContainer },
  } = theme.useToken();

  const items = [
    {
      key: "farmer",
      label: "Farmers",
      icon: <TeamOutlined style={{ fontSize: "20px" }} />,
      link: "/farmer",
    },
    {
      key: "farmManagement",
      label: "Farm Management",
      icon: <TeamOutlined style={{ fontSize: "20px" }} />,
      //link: "/farm-management",
      children: [
        {
          key: "configuration",
          label: "Configuration ",
          icon: <AlignCenterOutlined style={{ fontSize: "20px" }} />,
          link: "/configuration",
        },
        {
          key: "farmManagement",
          label: "Data",
          icon: <TeamOutlined style={{ fontSize: "20px" }} />,
          link: "/farm-management",
        }
      ],
    },
    {
      key: "tasks",
      label: "Tasks",
      icon: <ScheduleOutlined style={{ fontSize: "20px" }} />,
      // link: "/tasks",
      children: [
        {
          key: "tasks",
          label: "Tasks",
          icon: <AlignCenterOutlined style={{ fontSize: "20px" }} />,
          link: "/tasks",
        },
        {
          key: "activity_management",
          label: "Activity Management",
          icon: <ScheduleOutlined style={{ fontSize: "20px" }} />,
          link: "/activity-management",
        },
      ],
    },
    {
      key: "staffs",
      icon: <ContactsOutlined style={{ fontSize: "20px" }} />,
      label: "Staffs",
      link: "/staffs",
    },
    {
      key: "jobs-list",
      icon: <OrderedListOutlined style={{ fontSize: "20px" }} />,
      label: "Job List",
      link: "/jobs-list",
    },
    {
      key: "service-requests",
      label: "Raise SR",
      icon: <NodeExpandOutlined style={{ fontSize: "20px" }} />,
      link: "/service-requests",
    },
    {
      key: "bgLocations",
      label: "Locations",
      icon: <EnvironmentOutlined style={{ fontSize: "20px" }} />,
      // link: "/bgLocations",
      children: [
        {
          key: "staff_monthly_km_report",
          label: "Staff Monthly KM Report",
          icon: <CoffeeOutlined style={{ fontSize: "20px" }} />,
          link: "/staff-monthly-km-report",
        },
        {
          key: "staff_daily_km_report",
          label: "Staff Daily KM Report",
          icon: <CoffeeOutlined style={{ fontSize: "20px" }} />,
          link: "/staff-daily-km-report",
        },
        {
          key: "bgLocations",
          label: "Locations",
          icon: <NodeExpandOutlined style={{ fontSize: "20px" }} />,
          link: "/bgLocations",
        },
      ],
    },
    {
      key: "oc",
      label: "Open Commerce",
      icon: <BarsOutlined style={{ fontSize: "20px" }} />,
      link: "/oc",
    },
    {
      key: "freelancer",
      label: "Freelancers",
      type: "group",
      icon: <UserAddOutlined style={{ fontSize: "20px" }} />,
      children: [
        {
          key: "ppuService",
          label: "Onboard",
          icon: <CoffeeOutlined style={{ fontSize: "20px" }} />,
          link: "/ppuService",
        },
        {
          key: "ppuServiceRequest",
          label: "Raise SR",
          icon: <NodeExpandOutlined style={{ fontSize: "20px" }} />,
          link: "/ppuServiceRequest",
        },
      ],
    },
    {
      key: "ahv",
      label: "Vet Center",
      icon: <AimOutlined style={{ fontSize: "20px" }} />,
      link: "/ahv",
    },
    {
      key: "sr",
      label: "Smart Ration",
      icon: <CustomerServiceOutlined style={{ fontSize: "20px" }} />,
      link: "/sr",
    },
    {
      key: "cs",
      label: "Customer Care Services",
      icon: <CustomerServiceOutlined style={{ fontSize: "20px" }} />,
      link: "/cs",
    },
    {
      key: "gdd",
      label: "Govind Dairy Demo",
      icon: <CustomerServiceOutlined style={{ fontSize: "20px" }} />,
      link: "/gdd",
    },
    {
      key: "lm",
      label: "Lead Management",
      icon: <ApartmentOutlined style={{ fontSize: "20px" }} />,
      link: "/lm",
    },
    {
      key: "im",
      label: "Inventory Management",
      icon: <ClusterOutlined style={{ fontSize: "20px" }} />,
      link: "/im",
    },
    {
      key: "requisitionlist",
      label: "Requisiton List",
      icon: <ClusterOutlined style={{ fontSize: "20px" }} />,
      link: "/requisitionlist",
    },
    {
      key: "pharmacylist",
      label: "Pharmacy Open List",
      icon: <ClusterOutlined style={{ fontSize: "20px" }} />,
      link: "/pharmacylist",
    },
    {
      key: "pharmacyclosedlist",
      label: "Pharmacy Closed List",
      icon: <ClusterOutlined style={{ fontSize: "20px" }} />,
      link: "/pharmacyclosedlist",
    },
    {
      key: "medicineinventory",
      label: "Medicine Inventory",
      icon: <ClusterOutlined style={{ fontSize: "20px" }} />,
      link: "/medicineinventory",
    },
    {
      key: "ledgerlist",
      label: "Ledger List",
      icon: <ClusterOutlined style={{ fontSize: "20px" }} />,
      link: "/ledgerlist",
    },
    {
      key: "milk-projection",
      label: "Milk Projection",
      icon: <span role="img" aria-label="milk">🥛</span>,
      link: "/milk-projection",
    },
    {
      key: "logout",
      label: "Logout",
      icon: <LogoutOutlined style={{ fontSize: "20px" }} />,
      link: "#",
    },
  ];

  const getProfileImage = () => {
    if (userData) {
      if (userData.image === null) {
        setProfileImage(
          "https://cdn.pixabay.com/photo/2015/10/05/22/37/blank-profile-picture-973460__340.png"
        );
      } else {
        setProfileImage(BASE_URL + "/media/" + userData.image);
      }
    }
  };

  const profileClickHandler = () => {
    navigate("/profile");
  };

  const logoutHandler = async () => {
    await auth.signOut();
    dispatch(clearUserToken(navigate, "/login", "Logged out successfully!"));
  };

  useEffect(() => {
    getProfileImage();
  }, [userData]);

  return (
    <Layout>
      <Sider
        theme="light"
        breakpoint="lg"
        collapsedWidth="50"
        onBreakpoint={(broken) => {
          setCollapsed(broken);
        }}
        collapsed={collapsed}
        style={{
          height: "100vh",
          overflowY: "auto",
        }}
      >
        <Menu
          style={{ marginTop: "60px" }}
          defaultSelectedKeys={[
            `${window.location.href.split("/").slice(3)[0]}`,
          ]}
          mode="inline"
        >
          {items.map((item) => {
            if (item.type === "divider") {
              return <Menu.Divider key={item.key} />;
            }

            if (item.children) {
              return (
                <Menu.SubMenu
                  key={item.key}
                  icon={item.icon}
                  title={item.label}
                >
                  {item.children.map((child) => (
                    <Menu.Item key={child.key} title={child.label}>
                      <Link to={child.link} style={{ textDecoration: "none" }}>
                        {child.label}
                      </Link>
                    </Menu.Item>
                  ))}
                </Menu.SubMenu>
              );
            }

            return (
              <Menu.Item
                key={item.key}
                icon={item.icon}
                title={item.label}
                onClick={item.key === "logout" ? logoutHandler : undefined}
              >
                <Link to={item.link} style={{ textDecoration: "none" }}>
                  {item.label}
                </Link>
              </Menu.Item>
            );
          })}
        </Menu>
      </Sider>
      <Layout>
        <Header
          style={{
            padding: "0 20px 0 0",
            margin: "0 10px",
            background: colorBgContainer,
            paddingInline: 0,
            position: "fixed",
            top: 0,
            left: 0,
            width: "100%",
            zIndex: 1,
            display: "flex",
          }}
        >
          <div className="col-6 col-lg-6 col-md-6">
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              className="toggle-button"
            />
            <img
              // src={process.env.PUBLIC_URL + "krushal.png"}
              src={krushalLogo}
              style={{ width: "110px", marginLeft: "20px" }}
              className="logo"
              alt="krushal logo"
            />
          </div>
          <div className="col-5 col-lg-5 col-md-5"></div>
          <div className="col-1 col-lg-1 col-md-1 d-flex justify-content-center">
            <div
              className={
                location.pathname === "/farmer"
                  ? "dashboard__profile-img-active-new"
                  : "dashboard__profile-img-inactive-new"
              }
            >
              <div
                className="dashboard__img-div-new"
                onClick={profileClickHandler}
              >
                <img
                  alt=""
                  src={profileImage}
                  style={{ width: "100%", height: "100%" }}
                />
              </div>
            </div>
          </div>
        </Header>
        <div
          style={{
            height: "calc(100vh - 64px)",
            overflowY: "auto",
            width: "100%",
            margin: "64px 0 0 0",
            padding: "0 10px",
          }}
        >
          {props.content}
        </div>
      </Layout>
    </Layout>
  );
};
export default Sidebar;

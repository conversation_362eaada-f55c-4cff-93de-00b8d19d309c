import "./App.css";
import { useEffect, useState, lazy } from "react";
import { Routes, Route, Navigate, useNavigate } from "react-router-dom";
import { ToastContainer } from "react-toastify";
import { useSelector, useDispatch } from "react-redux";

// Components
import Login from "../src/Components/user/components/Login";
import AccessDenied from "./Utilities/AccessDenied";
import {
  updateUserToken,
  updateFirebaseUid,
  updateUserAppGroups,
} from "./Components/user/action";
import { auth } from "./Config/firebase.config";

import MapWrapper from "./Components/Dashboard/BgLocation/MapWrapper";
import { checkAccessToken } from "./Components/user/action";
// import Dashboard from "./Components/Dashboard/Dashboard";
import Farmers from "./Components/Dashboard/Farmers/Farmers";
import Sidebar from "./Components/Sidebar/Sidebar";
import AddFarmer1 from "./Components/Dashboard/Farmers/AddFarmer/AddFarmer1";
import FarmerItem1 from "./Components/Dashboard/Farmers/FarmerItem/FarmerItem1";
import AddCattle1 from "./Components/Dashboard/Cattle/AddCattle/AddCattle1";
import CattleItem1 from "./Components/Dashboard/Cattle/CattleItem/CattleItem1";
import Tasks from "./Components/Dashboard/Tasks/Tasks";
import AddTask from "./Components/Dashboard/Tasks/AddTask/AddTask";
import AddFarmerTask from "./Components/Dashboard/Farmers/FarmerItem/AddFarmerTask/AddFarmerTask";
import TaskItem from "./Components/Dashboard/Tasks/TaskItem/TaskItem";
import AddStaff from "./Components/Dashboard/Staffs/AddStaff/AddStaff";
import StaffItem from "./Components/Dashboard/Staffs/StaffItem/StaffItem";
import ServiceRequests from "./Components/Dashboard/ServiceRequests/ServiceRequests";
import JobList from "./Components/Dashboard/JobList/JobList";
import Profile from "./Components/Dashboard/Profile/Profile";
import PpuService from "./Components/Dashboard/PPU/PpuService";
import PpuRaiseSr from "./Components/Dashboard/PPU/PpuRaiseSr";
import OpenCommerceRoutes from "./OpenCommerce/OpenCommerceRoutes";
import RequisitionList from "./Components/Dashboard/Requisition/RequisitionList";
import AddRequisition from "./Components/Dashboard/Requisition/AddRequisition/AddRequisition";
// import RequisitionDetails from "./Components/Dashboard/Requisition/RequisitionDetails/RequisitionDetails";
import RequisitionDetailsNew from "./Components/Dashboard/Requisition/RequisitionDetails/RequisitionDetailsNew";

import PharmacyList from "./Components/Dashboard/Pharmacy/PharmacyList";
import MedicineInventory from "./Components/Dashboard/MedicineInventory/MedicineInventory";
import LedgerList from "./Components/Dashboard/Ledger/LedgerList";
import PharmacyRequisitionDetails from "./Components/Dashboard/Pharmacy/PharmacyRequsitionDetails/PharmacyRequisitionDetails";
import PharmacyClosedList from "./Components/Dashboard/Pharmacy/PharmacyClosedList";
import Invoice from "./Components/Dashboard/Pharmacy/Invoice/Invoice";
/* import OpenCommerce from "./OpenCommerce/Components/OpenCommerce";
import FarmersNoLocationRecorded from "./OpenCommerce/Components/CattleExchange/FarmersNoLocationRecorded";
import FarmersLocationRecorded from "./OpenCommerce/Components/CattleExchange/FarmersLocationRecorded";
import DataCollectionOfPotentiallySellableAnimals from "./OpenCommerce/Components/CattleExchange/DataCollectionOfPotentiallySellableAnimals";
import SellableAnimals from "./OpenCommerce/Components/CattleExchange/SellableAnimals";
import PotentiallySellableAnimals from "./OpenCommerce/Components/CattleExchange/PotentiallySellableAnimals";
import AnimalsThatFarmersMayWantToSell from "./OpenCommerce/Components/CattleExchange/AnimalsThatFarmersMayWantToSell";
import PotentiallyBuyerFarmersForAnimal from "./OpenCommerce/Components/CattleExchange/PotentiallyBuyerFarmersForAnimal";
import FarmersWhoMayWantToBuy from "./OpenCommerce/Components/CattleExchange/FarmersWhoMayWantToBuy";
import AnimalExchangeOrders from "./OpenCommerce/Components/CattleExchange/AnimalExchangeOrders";
import ManureOCFarmersNotContacted from "./OpenCommerce/Components/Manure/ManureOCFarmersNotContacted";
import ManureOCFarmersNotInterested from "./OpenCommerce/Components/Manure/ManureOCFarmersNotInterested";
import ManureOCFarmersInterested from "./OpenCommerce/Components/Manure/ManureOCFarmersInterested";
 */

/* import AHVet from "./AHVet/Components/AHVet";
import AnimalListingForVeterinarians from "./AHVet/Components/AnimalListingForVeterinarians";
import MedicineList from "./AHVet/Components/MedicineList";
 */
import AHVetRoutes from "./AHVet/AHVetRoutes";

/* import LeadManagement from "./LeadManagement/Components/LeadManagement";
import OCFarmerDetails from "./Entities/Components/OCFarmerDetails";
import OCAnimalDetails from "./Entities/Components/OCAnimalDetails";
 */
import EntitiesRoutes from "./Entities/EntitiesRoutes";

/* import CustomerService from "./CustomerService/Components/CustomerService";
import CustomerServiceTasks from "./CustomerService/Components/CustomerServiceTasks";
 */
import CustomerServiceRoutes from "./CustomerService/CustomerServiceRoutes";
import GovindDairyDemoRoutes from "./CustomerService/GovindDairyDemoRoutes";

import Staffs from "./Components/Dashboard/Staffs/Staff";
import { pdfjs } from "react-pdf";
import {
  loadReferenceForMedicine,
  getCareCalendarReferencesData,
} from "./Utilities/Common/common.router";

/* import SmartRationCustomers from "./LeadManagement/Components/SmartRationCustomers";
import DirectlyContactedFarmerLeads from "./LeadManagement/Components/DirectlyContactedFarmerLeads";
import BCOList from "./LeadManagement/Components/BCOList";
import LMBCO from "./LeadManagement/Components/LMBCO";
 */
import SmartRationRoutes from "./SmartRation/SmartRationRoutes";

/* import InventoryManagement from "./InventoryManagement/Components/InventoryManagement";
import PharmacyList from "./InventoryManagement/Components/PharmacyList";
import IMPharmacy from "./InventoryManagement/Components/IMPharmacy"; */
import InventoryManagementRoutes from "./InventoryManagement/InventoryManagementRoutes";
import LeadManagementRoutes from "./LeadManagement/LeadManagementRoutes";
import IMPharmacy from "./InventoryManagement/Components/IMPharmacy";
import StaffDailyKMReport from "./Components/Dashboard/BgLocation/StaffDailyKMReport";
import StaffMonthlyKMReport from "./Components/Dashboard/BgLocation/StaffMonthlyKMReport";
import ActivityManagement from "./Components/Dashboard/activityManagement/ActivityManagement";
import EditActivity from "./Components/Dashboard/activityManagement/editActivity/EditActivity";
import ScheduleTask from "./Components/Dashboard/Tasks/ScheduleTask/ScheduleTask";
import { useGetActivityQuery } from "./Components/Dashboard/activityManagement/reducer/activityManagementSlice";
import AddCDPLFarmers from "Components/Dashboard/Farmers/cdpl-farmer/add-cdpl-farmers/AddCDPLFarmers";
import UpdateCDPLFarmer from "Components/Dashboard/Farmers/cdpl-farmer/update-cdpl-farmer/UpdateCDPLFarmer";
import UpdateCDPLCattle from "Components/Dashboard/Cattle/cdpl-cattle/UpdateCDPLCattle";
import AddCDPLCattle from "./Components/Dashboard/Cattle/cdpl-cattle/add-cdpl-cattle/AddCDPLCattle";
import Version from "./Utilities/Version";
import ServiceRequestsV2 from "./Components/Dashboard/ServiceRequests/v2/ServiceRequestsV2";
import _ from "lodash";
import FarmManagement from "./Components/Dashboard/farm-management";
import FmTasks from "./Components/Dashboard/farm-management/components/task";
import FarmManagementTopics from "./Components/Dashboard/farm-management/components/topics";
import FarmManagementConfiguration from './Components/Dashboard/farm-management-configuration/index';
import QuestionDetails from './Components/Dashboard/farm-management-configuration/list/QuestionDetails';
import MilkProjection from './Components/Dashboard/MilkProjection/MilkProjection';
import FarmerAnimals from './Components/Dashboard/MilkProjection/FarmerAnimals';

pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js`;

const {
  assignL10NObjectOrObjectArrayToItSelf,
} = require("@krushal-it/common-core");
const { loadReferencesForReferenceCategories } = require("./router");
const { authenticateFromBackend } = require("./Components/user/authorize");

global.references = {};
global.userPreferences = {};
// referenceCategoryToReferencesMap
const loadReferenceData = async (userToken) => {
  try {
    const headers = {
      useCase: "Get Reference Data",
      token: userToken.accessToken, //authToken
      refreshtoken: localStorage.getItem("refreshToken"),
    };
    const referencesRelatedData = {};
    referencesRelatedData.reference_category_array = [
      10002300, 10008900, 10006500, 10006600, 10006700, 10006800, 10006200,
      10005900, 10006100, 10006300, 10006400, 10007700, 10007800, 10005600,
      10005700, 10005800, 10008100, 10008000, 10002300, 10001100, 10001200,
      10001300, 10013000, 10013005, 10003000, 20000000, 10003700, 10003600,
      10001400, 10001500, 10001050, 10002400, 10010000, 10002000, 10001700,
      10001800, 10030000, 10001900, 10020000, 10008600, 10001000, 10009500,
      10009600, 10011000, 10008900, 10006500, 10006600, 10006700, 10006800,
      10011500, 10012400, 10002700, 10013200, 10013100, 10012900, 10013400,
      10006000, 10700100, 10700200, 10700300, 10700400, 10700500, 10700600,
      10700700, 10700800, 10700900, 10701000, 10701100, 10701200, 10701300,
      10701400, 10701500, 10701600, 10701700, 10701800, 10701900, 10702000,
      10702100, 10702200, 10702300, 10702400, 10702500, 10702600, 10702700,
      10702800, 10702900, 10703000, 10703100, 10703200, 10703300, 10703400,
      10703500, 10703600, 10703700, 10703800, 10703900, 10704000, 10704100,
      10704200, 10704300, 10704400, 10704500, 10704600, 10704700, 10704800,
      10704900, 10705000, 10705100, 10705200, 10705300, 10705400, 10705500,
      10705600, 10705700, 10705800, 10705900, 10706000, 10706100, 10011600,
      10706200,10706700,10001000
    ];
    // referencesRelatedData.reference_category_array = [
    //   10001300, 10013000, 10001050,
    //   10001000, 10009500, 10009600, 10011000, 10008900, 10006500, 10006600,
    //   10006700, 10006800, 10006200, 10005900, 10006100, 10006300, 10006400,
    //   10007700, 10007800, 10005600, 10005700, 10005800, 10008100, 10008000]
    referencesRelatedData.crop_list = true;
    referencesRelatedData.period = true;
    const response = await loadReferencesForReferenceCategories(
      headers,
      referencesRelatedData
    );
    console.log(response.data, 'refrnce check')
    return response.data;
  } catch (error) {
    console.log("SA lR 10, error");
    console.log("SA lR 10, error = ", error);
    throw error;
  }
};

global.referencesWithoutCategoryId = {};
global.activityReferences = {};


function App() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const userToken = useSelector((state) => state.user.userToken);
  const appGroups = useSelector((state) => state.user.appGroups);
  const firebaseUid = useSelector((state) => state.user.firebaseUid);
  const [reloadNumber, setReloadNumber] = useState(0);
  // const { data } = useGetActivityQuery()

  const loadReferences = async (userToken) => {
    const loadReferenceDataResponse = await loadReferenceData(userToken);
    console.log("loadReferenceDataResponse", loadReferenceDataResponse);
    if (loadReferenceDataResponse.return_code === 0) {
      if (loadReferenceDataResponse.reference_category_map) {
        global.references.referenceCategoryToReferencesMap =
          loadReferenceDataResponse.reference_category_map;
      }
      if (loadReferenceDataResponse.crops) {
        // global.references.crops = assignL10NObjectOrObjectArrayToItSelf(loadReferenceDataResponse.crops, 'crop_name_l10n')
        global.references.crops = loadReferenceDataResponse.crops;
        /* const cropsForDropdown = []
        for (const crop of global.references.crops) {
          const cropForDropdown = {}
          cropForDropdown['value'] = crop.crop_id
          cropForDropdown['label'] = crop.crop_name_l10n
          cropsForDropdown.push(cropForDropdown)
        }
        global.references.crops_dropdown = cropsForDropdown */
      }
      setReloadNumber(reloadNumber + 1);
    }
  };

  const getCareCalendarReferences = async () => {
    // calling carecalendar reference api and setting in global state
    global.referencesWithoutCategoryId.referenceMap =
      await getCareCalendarReferencesData(dispatch, navigate);
  };

  const getMedicineReference = async () => {
    global.medicineReferences = await loadReferenceForMedicine(
      dispatch,
      navigate
    );
  };

  const getUserAccessGroups = async (user, dispatch) => {
    const authenticationResponse = await authenticateFromBackend(
      user,
      dispatch
    );
    if (authenticationResponse.return_code !== 0) {
      throw new Error("No access groups for user");
    }
  };

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged((user) => {
      if (user) {
        dispatch(
          updateUserToken(
            user.accessToken,
            user.stsTokenManager.refreshToken,
            navigate
          )
        );
        dispatch(updateFirebaseUid(user.uid));
        getUserAccessGroups(user, dispatch, navigate);
        // User is signed in
        // setUser(user);
      } else {
        // User is signed out
        // setUser(null);
        navigate("/login");
      }
    });

    return () => {
      unsubscribe();
    };
  }, []);

  const { data, isFetching, isLoading } = useGetActivityQuery(null, {
    skip: !userToken,
  });

  const getActivityReferences = () => {
    if (data) {
      const groupedActivities = _.groupBy(data?.report, "activity_category");
      global.activityReferences = groupedActivities;
    }
  };

  useEffect(() => {
    getActivityReferences();
  }, [isLoading, isFetching]);

  useEffect(() => {
    if (userToken === undefined || userToken === null) {
      // dispatch(checkAccessToken(navigate));
    } else {
      if (global.references && Object.keys(global.references).length === 0) {
        loadReferences(userToken);
        getCareCalendarReferences();
        getMedicineReference();
      }
    }
  }, [userToken]);
  console.log(global.activityReferences);
  const globalReferencesLoaded =
    global.references &&
    global.references.referenceCategoryToReferencesMap &&
    Object.keys(global.references.referenceCategoryToReferencesMap).length > 0;
  return userToken === null ||
    userToken === undefined ||
    !globalReferencesLoaded ? (
    <Routes>
      <Route path="/login" element={<Login />}></Route>
      <Route path="/access-denied" element={<AccessDenied />} />
      <Route path="/version" element={<Version />} />
    </Routes>
  ) : (
    <div className="App">
      <Routes>
        {/* Login route */}
        <Route path="/login" element={<Login />}></Route>
        <Route path="/access-denied" element={<AccessDenied />} />
        <Route path="/version" element={<Version />} />

        {Object.keys(global?.references)?.length > 1 && (
          <>
            {/* Home route */}
            <Route path="/" element={<Navigate replace to="/farmer" />}></Route>

            {/* Other routes */}
            {/* <Route path="/dashboard/*" element={<Dashboard reloadNumber={reloadNumber} />} /> */}
            <Route path="/locations" element={<MapWrapper />} />

            {/* Farmer related routes */}
            <Route path="/farmer" element={<Sidebar content={<Farmers />} />} />
            <Route
              path="/add-farmer1"
              element={<Sidebar content={<AddCDPLFarmers />} />}
            />
            <Route
              path="/farmer/:farmerId"
              element={<Sidebar content={<UpdateCDPLFarmer />} />}
            />
            <Route
              path="/farmer/:farmerId/cattle/add-cattle"
              element={<Sidebar content={<AddCDPLCattle />} />}
            />
            <Route
              path="/farmer/:farmerId/cattle/:cattleId"
              element={<Sidebar content={<UpdateCDPLCattle />} />}
            />
            <Route
              path="/farmer/:farmerId/cattle/:cattleId/tasks/add-task"
              element={<Sidebar content={<AddTask />} />}
            />
            <Route
              path="/farmer/:farmerId/tasks/add-task"
              element={<Sidebar content={<AddFarmerTask />} />}
            />

            {/* Task related routes */}
            <Route path="/tasks" element={<Sidebar content={<Tasks />} />} />
            <Route
              path="/tasks/:taskId"
              element={<Sidebar content={<TaskItem />} />}
            />

            {/* Staff related routes */}
            <Route path="/staffs" element={<Sidebar content={<Staffs />} />} />
            <Route
              path="/staffs/add-staff"
              element={<Sidebar content={<AddStaff />} />}
            />
            <Route
              path="/staffs/:staffId"
              element={<Sidebar content={<StaffItem />} />}
            />

            {/* Some other routes */}
            <Route
              path="/service-requests"
              element={<Sidebar content={<ServiceRequestsV2 />} />}
            />
            <Route
              path="/jobs-list"
              element={<Sidebar content={<JobList />} />}
            />
            <Route
              path="/profile"
              element={<Sidebar content={<Profile />} />}
            />
            <Route
              path="/bgLocations"
              element={<Sidebar content={<MapWrapper />} />}
            />
            <Route
              path="/staff-daily-km-report"
              element={<Sidebar content={<StaffDailyKMReport />} />}
            />
            <Route
              path="/staff-monthly-km-report"
              element={<Sidebar content={<StaffMonthlyKMReport />} />}
            />

            {/* PPU related routes */}
            <Route
              path="/ppuService"
              element={<Sidebar content={<PpuService />} />}
            />
            <Route
              path="/ppuServiceRequest"
              element={<Sidebar content={<PpuRaiseSr />} />}
            />

            {/* requisition related routes */}
            <Route
              path="/requisitionlist"
              element={<Sidebar content={<RequisitionList />} />}
            />
            {/* <Route path="/requisitionlist/requisitiondetails" element={<Sidebar content={<RequisitionDetails />} />} /> */}
            <Route
              path="/requisitionlist/requisitiondetails/:id"
              element={<Sidebar content={<RequisitionDetailsNew />} />}
            />
            <Route
              path="/requisitionlist/addrequisition"
              element={<Sidebar content={<AddRequisition />} />}
            />

            {/* medicine inventory related routes */}
            <Route
              path="/medicineinventory"
              element={<Sidebar content={<MedicineInventory />} />}
            />

            {/* pharmacy related routes */}
            <Route
              path="/pharmacylist"
              element={<Sidebar content={<PharmacyList />} />}
            />
            <Route
              path="/pharmacylist/pharmacyrequisitiondetails"
              element={<Sidebar content={<PharmacyRequisitionDetails />} />}
            />
            <Route
              path="/pharmacylist/pharmacyrequisitiondetails/invoice"
              element={<Sidebar content={<Invoice />} />}
            />

            <Route
              path="/pharmacyclosedlist"
              element={<Sidebar content={<PharmacyClosedList />} />}
            />
            <Route
              path="/pharmacyclosedlist/pharmacyrequisitiondetails"
              element={<Sidebar content={<PharmacyRequisitionDetails />} />}
            />

            {/* ledger list */}
            <Route
              path="ledgerlist"
              element={<Sidebar content={<LedgerList />} />}
            />

            {/* Activity Management */}
            <Route
              path="/activity-management"
              element={<Sidebar content={<ActivityManagement />} />}
            />
            <Route
              path="/activity-management/:activity_id"
              element={<Sidebar content={<EditActivity />} />}
            />

            {/* Schedule Task */}
            {/* <Route
              path="/schedule-task-old"
              element={<Sidebar content={<CreateActivityForm />} />}
            /> */}
            <Route
              path="/schedule-task"
              element={<Sidebar content={<ScheduleTask />} />}
            />

            {/* Farm Management */}
            <Route
              path="/farm-management"
              element={<Sidebar content={<FarmManagement />} />}
            />
            <Route
              path="/farm-management/task-list/:customerId"
              element={<Sidebar content={<FmTasks />} />}
            />
            <Route
              path="/farm-management/task-list/:customerId/topics/:taskId"
              element={<Sidebar content={<FarmManagementTopics />} />}
            />
            {/* configuration */}

            <Route
              path="/configuration"
              element={<Sidebar content={< FarmManagementConfiguration />} />}
            />
            <Route
              path="/configuration/question-details/:id"
              element={<Sidebar content={<QuestionDetails />} />}
            />

            {/* New route for Milk Projection */}
            <Route
              path="/milk-projection"
              element={<Sidebar content={<MilkProjection />} />}
            />
            <Route
              path="/farmer/:farmerId/animals"
              element={<Sidebar content={<FarmerAnimals />} />}
            />
          </>
        )}
      </Routes>
      <OpenCommerceRoutes />
      <AHVetRoutes />
      <LeadManagementRoutes />
      <SmartRationRoutes />
      <EntitiesRoutes />
      <CustomerServiceRoutes />
      <GovindDairyDemoRoutes />
      <InventoryManagementRoutes />

      <ToastContainer />
    </div>
  );
  // TODO:remove the locations route when done testing
}

export default App;

